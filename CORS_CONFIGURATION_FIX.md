# CORS Configuration Fix for Audio Quiz Application

## Issue Description

The current CORS configuration on the backend server is using a wildcard (`*`) for `Access-Control-Allow-Headers`, which doesn't properly support the `Authorization` header when credentials are being sent. This causes authentication issues when the frontend tries to send requests with Bearer tokens.

## Problem

When using `Access-Control-Allow-Credentials: true`, browsers require explicit header names in `Access-Control-Allow-Headers` instead of wildcards for security reasons.

## Solution

Update the backend CORS configuration to explicitly include the `Authorization` header along with other required headers.

### For Express.js Backend

If using the `cors` middleware:

```javascript
const cors = require('cors');

const corsOptions = {
  origin: [
    'http://localhost:3000',
    'http://localhost:8203',
    // Add your production domain here
    'https://your-production-domain.com'
  ],
  credentials: true,
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
```

### Manual CORS Headers

If setting headers manually:

```javascript
app.use((req, res, next) => {
  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:8203',
    'https://your-production-domain.com'
  ];
  
  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization, X-Requested-With, Accept, Origin'
  );
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});
```

### For Other Frameworks

#### Fastify
```javascript
await fastify.register(require('@fastify/cors'), {
  origin: ['http://localhost:3000', 'http://localhost:8203'],
  credentials: true,
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
});
```

#### Koa.js
```javascript
const cors = require('@koa/cors');

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:8203'],
  credentials: true,
  allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
}));
```

## Testing the Fix

After implementing the CORS fix, test it by:

1. Opening the browser developer tools
2. Making an authenticated request from the frontend
3. Checking that there are no CORS errors in the console
4. Verifying that the `Authorization` header is being sent and accepted

## Important Notes

1. **Never use wildcards** (`*`) for `Access-Control-Allow-Headers` when `credentials: true`
2. **Explicitly list all required headers** including `Authorization`
3. **Specify exact origins** instead of using wildcard for `Access-Control-Allow-Origin` when using credentials
4. **Handle preflight requests** properly by responding to OPTIONS requests

## Environment-Specific Configuration

Consider using environment variables for different CORS settings:

```javascript
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-production-domain.com']
    : ['http://localhost:3000', 'http://localhost:8203'],
  credentials: true,
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ]
};
```

This fix should resolve the CORS issues with the Authorization header and allow proper authentication between the frontend and backend.
