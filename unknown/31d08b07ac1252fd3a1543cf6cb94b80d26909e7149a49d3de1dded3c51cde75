import React, { useEffect, useRef } from 'react'
import * as anime from 'animejs'
import { cn } from '../../utils/cn'

interface AnimatedCharacterProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  character?: 'wizard' | 'hero' | 'student' | 'scientist' | 'artist' | 'astronaut'
  mood?: 'happy' | 'excited' | 'thinking' | 'celebrating' | 'focused'
  className?: string
}

/**
 * Animated Character Component with anime.js animations
 * Creates beautiful animated characters for user profiles
 */
const AnimatedCharacter: React.FC<AnimatedCharacterProps> = ({
  size = 'md',
  character = 'wizard',
  mood = 'happy',
  className
}) => {
  const characterRef = useRef<HTMLDivElement>(null)
  const eyesRef = useRef<HTMLDivElement>(null)
  const sparklesRef = useRef<HTMLDivElement>(null)
  const auraRef = useRef<HTMLDivElement>(null)

  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
    xl: 'w-40 h-40'
  }

  const characters = {
    wizard: { emoji: '🧙‍♂️', color: 'from-purple-500 to-blue-600' },
    hero: { emoji: '🦸‍♀️', color: 'from-red-500 to-orange-600' },
    student: { emoji: '👨‍🎓', color: 'from-blue-500 to-green-600' },
    scientist: { emoji: '🧑‍🔬', color: 'from-green-500 to-teal-600' },
    artist: { emoji: '👩‍🎨', color: 'from-pink-500 to-purple-600' },
    astronaut: { emoji: '🧑‍🚀', color: 'from-indigo-500 to-purple-600' }
  }

  useEffect(() => {
    if (!anime.default) return

    // Main character floating animation
    if (characterRef.current) {
      anime.default({
        targets: characterRef.current,
        translateY: [-8, 8],
        duration: 2000,
        loop: true,
        direction: 'alternate',
        easing: 'easeInOutSine'
      })
    }

    // Eyes blinking animation
    if (eyesRef.current) {
      anime.default({
        targets: eyesRef.current,
        scaleY: [1, 0.1, 1],
        duration: 150,
        delay: 2000,
        loop: true,
        easing: 'easeInOutQuad'
      })
    }

    // Sparkles animation
    if (sparklesRef.current) {
      anime.default({
        targets: sparklesRef.current.children,
        scale: [0, 1, 0],
        opacity: [0, 1, 0],
        rotate: [0, 180, 360],
        duration: 1500,
        delay: anime.default.stagger(200),
        loop: true,
        easing: 'easeInOutSine'
      })
    }

    // Aura pulsing animation
    if (auraRef.current) {
      anime.default({
        targets: auraRef.current,
        scale: [1, 1.2, 1],
        opacity: [0.3, 0.6, 0.3],
        duration: 3000,
        loop: true,
        easing: 'easeInOutSine'
      })
    }

    // Mood-based animations
    const moodAnimations = {
      excited: () => {
        if (characterRef.current) {
          anime.default({
            targets: characterRef.current,
            scale: [1, 1.1, 1],
            rotate: [-5, 5, -5, 0],
            duration: 800,
            loop: true,
            easing: 'easeInOutElastic(1, .6)'
          })
        }
      },
      celebrating: () => {
        if (characterRef.current) {
          anime.default({
            targets: characterRef.current,
            translateY: [-20, 0],
            scale: [1, 1.2, 1],
            rotate: [0, 360],
            duration: 1000,
            loop: true,
            easing: 'easeOutBounce'
          })
        }
      },
      thinking: () => {
        if (characterRef.current) {
          anime.default({
            targets: characterRef.current,
            rotate: [-2, 2, -2, 0],
            duration: 2000,
            loop: true,
            easing: 'easeInOutSine'
          })
        }
      }
    }

    // Apply mood animation
    if (moodAnimations[mood]) {
      setTimeout(() => moodAnimations[mood](), 500)
    }

  }, [mood])

  const generateSparkles = () => {
    return Array.from({ length: 6 }, (_, i) => (
      <div
        key={i}
        className="absolute text-yellow-400"
        style={{
          left: `${10 + (i * 15)}%`,
          top: `${5 + (i % 3) * 30}%`,
          fontSize: '12px'
        }}
      >
        ✨
      </div>
    ))
  }

  return (
    <div className={cn('relative inline-block', className)}>
      {/* Aura/Glow Effect */}
      <div
        ref={auraRef}
        className={cn(
          'absolute inset-0 rounded-full blur-md opacity-30',
          `bg-gradient-to-br ${characters[character].color}`,
          sizeClasses[size]
        )}
      />

      {/* Sparkles */}
      <div ref={sparklesRef} className="absolute inset-0 pointer-events-none">
        {generateSparkles()}
      </div>

      {/* Main Character */}
      <div
        ref={characterRef}
        className={cn(
          'relative rounded-full flex items-center justify-center text-white font-bold shadow-2xl border-4 border-white/20 backdrop-blur-sm',
          `bg-gradient-to-br ${characters[character].color}`,
          sizeClasses[size]
        )}
        style={{
          fontSize: size === 'xl' ? '4rem' : size === 'lg' ? '3rem' : size === 'md' ? '2rem' : '1.5rem'
        }}
      >
        {characters[character].emoji}
        
        {/* Eyes overlay for blinking */}
        <div
          ref={eyesRef}
          className="absolute inset-0 flex items-center justify-center"
          style={{ fontSize: '0.3em' }}
        >
          <span className="opacity-80">👀</span>
        </div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 4 }, (_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/60 rounded-full animate-ping"
            style={{
              left: `${20 + (i * 20)}%`,
              top: `${15 + (i % 2) * 60}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: '2s'
            }}
          />
        ))}
      </div>

      {/* Character name badge */}
      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
        <div className="bg-black/20 backdrop-blur-sm rounded-full px-2 py-1">
          <span className="text-xs text-white/80 capitalize font-medium">
            {character}
          </span>
        </div>
      </div>
    </div>
  )
}

export default AnimatedCharacter
