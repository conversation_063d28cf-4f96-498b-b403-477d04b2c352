import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// Types for our HTTP service
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  status: number
}

export interface ApiError {
  message: string
  status: number
  code?: string
}

export interface RequestCallbacks<T = any> {
  onSuccess?: (data: T) => void
  onError?: (error: ApiError) => void
  onLoading?: (loading: boolean) => void
}

class HttpBase {
  private instance: AxiosInstance
  private baseURL: string

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api'

    console.log('🌐 API Base URL:', this.baseURL)

    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        
        console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('❌ Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        console.error(`❌ ${error.response?.status || 'Network'} ${error.config?.url}`)
        
        // Handle 401 - Unauthorized
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
        }
        
        return Promise.reject(error)
      }
    )
  }

  // Generic request method with callbacks
  async request<T = any>(
    config: AxiosRequestConfig,
    callbacks?: RequestCallbacks<T>
  ): Promise<ApiResponse<T>> {
    try {
      callbacks?.onLoading?.(true)
      
      const response = await this.instance.request<T>(config)
      
      const apiResponse: ApiResponse<T> = {
        data: response.data,
        success: true,
        status: response.status,
        message: 'Request successful'
      }
      
      callbacks?.onSuccess?.(response.data)
      return apiResponse
      
    } catch (error: any) {
      const apiError: ApiError = {
        message: error.response?.data?.message || error.message || 'An error occurred',
        status: error.response?.status || 500,
        code: error.response?.data?.code
      }
      
      callbacks?.onError?.(apiError)
      throw apiError
      
    } finally {
      callbacks?.onLoading?.(false)
    }
  }

  // Convenience methods
  async get<T = any>(url: string, config?: AxiosRequestConfig, callbacks?: RequestCallbacks<T>) {
    return this.request<T>({ ...config, method: 'GET', url }, callbacks)
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig, callbacks?: RequestCallbacks<T>) {
    return this.request<T>({ ...config, method: 'POST', url, data }, callbacks)
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig, callbacks?: RequestCallbacks<T>) {
    return this.request<T>({ ...config, method: 'PUT', url, data }, callbacks)
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig, callbacks?: RequestCallbacks<T>) {
    return this.request<T>({ ...config, method: 'DELETE', url }, callbacks)
  }

  // Set auth token
  setAuthToken(token: string) {
    localStorage.setItem('auth_token', token)
    this.instance.defaults.headers.Authorization = `Bearer ${token}`
  }

  // Clear auth token
  clearAuthToken() {
    localStorage.removeItem('auth_token')
    delete this.instance.defaults.headers.Authorization
  }
}

// Export singleton instance
export const httpBase = new HttpBase()
export default httpBase
