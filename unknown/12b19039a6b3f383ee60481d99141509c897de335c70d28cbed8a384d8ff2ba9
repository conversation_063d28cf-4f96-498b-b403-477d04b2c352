import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      'animejs': path.resolve(__dirname, './node_modules/animejs/lib/anime.esm.js'),
      // Ensuring React is resolved correctly, can sometimes help with conflicts
      // 'react': path.resolve(__dirname, './node_modules/react'),
      // 'react-dom': path.resolve(__dirname, './node_modules/react-dom'),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json']
  },
  server: {
    port: 3000,
    // HMR (Hot Module Replacement) options for better development experience
    hmr: {
      overlay: true, // Show error overlay in the browser
    },
  },
  // esbuild options for JSX handling - Vite defaults are usually fine with @vitejs/plugin-react-swc
  // esbuild: {
  //   jsxInject: `import React from 'react'`,
  //   jsx: 'automatic',
  // },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'animejs',
      '@improbable-eng/grpc-web' // Keep this if it's a direct dependency that needs optimization
    ],
    // esbuildOptions: { // Usually not needed when using plugin-react-swc defaults
    //   define: {
    //     global: 'globalThis',
    //   },
    // },
  },
  build: {
    // CommonJS options might still be needed for gRPC generated code if it uses require
    commonjsOptions: {
      transformMixedEsModules: true,
      include: [
        /node_modules/,
        /src\/generated\/grpc\/.*\.js$/, // If you have gRPC JS files here
      ],
      extensions: ['.js', '.cjs'],
      requireReturnsDefault: 'auto' // Or true, or 'namespace' depending on module structure
    }
  }
})
