import React, { createContext, useContext, useState, useEffect } from 'react'
import { useLocation } from 'react-router-dom'

export interface NavigationBreadcrumb {
  id: string
  name: string
  path: string
  type: 'taskset' | 'taskitem'
  isActive: boolean
}

interface NavigationContextType {
  breadcrumbs: NavigationBreadcrumb[]
  setBreadcrumbs: (breadcrumbs: NavigationBreadcrumb[]) => void
  addBreadcrumb: (breadcrumb: Omit<NavigationBreadcrumb, 'isActive'>) => void
  clearBreadcrumbs: () => void
  currentTaskSet: { id: string; name: string } | null
  setCurrentTaskSet: (taskSet: { id: string; name: string } | null) => void
  currentTaskItem: { id: string; name: string } | null
  setCurrentTaskItem: (taskItem: { id: string; name: string } | null) => void
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined)

export const useNavigation = () => {
  const context = useContext(NavigationContext)
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider')
  }
  return context
}

interface NavigationProviderProps {
  children: React.ReactNode
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const [breadcrumbs, setBreadcrumbsState] = useState<NavigationBreadcrumb[]>([])
  const [currentTaskSet, setCurrentTaskSet] = useState<{ id: string; name: string } | null>(null)
  const [currentTaskItem, setCurrentTaskItem] = useState<{ id: string; name: string } | null>(null)
  const location = useLocation()

  // Clear navigation state when leaving task-related pages
  useEffect(() => {
    if (!location.pathname.startsWith('/tasks/')) {
      setBreadcrumbsState([])
      setCurrentTaskSet(null)
      setCurrentTaskItem(null)
    }
  }, [location.pathname])

  const setBreadcrumbs = (newBreadcrumbs: NavigationBreadcrumb[]) => {
    // Mark the last breadcrumb as active
    const updatedBreadcrumbs = newBreadcrumbs.map((breadcrumb, index) => ({
      ...breadcrumb,
      isActive: index === newBreadcrumbs.length - 1
    }))
    setBreadcrumbsState(updatedBreadcrumbs)
  }

  const addBreadcrumb = (breadcrumb: Omit<NavigationBreadcrumb, 'isActive'>) => {
    setBreadcrumbsState(prev => {
      // Mark all existing breadcrumbs as inactive
      const updatedPrev = prev.map(b => ({ ...b, isActive: false }))
      // Add new breadcrumb as active
      return [...updatedPrev, { ...breadcrumb, isActive: true }]
    })
  }

  const clearBreadcrumbs = () => {
    setBreadcrumbsState([])
    setCurrentTaskSet(null)
    setCurrentTaskItem(null)
  }

  const value: NavigationContextType = {
    breadcrumbs,
    setBreadcrumbs,
    addBreadcrumb,
    clearBreadcrumbs,
    currentTaskSet,
    setCurrentTaskSet,
    currentTaskItem,
    setCurrentTaskItem
  }

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  )
}

export default NavigationContext
