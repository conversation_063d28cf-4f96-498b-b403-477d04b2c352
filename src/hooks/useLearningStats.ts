import { useState, useEffect } from 'react'
import {
  LearningStats,
  LeaderboardResponse,
  LeaderboardParams,
  statsService
} from '../services/stats/statsService'

interface UseLearningStatsReturn {
  stats: LearningStats | null
  loading: boolean
  error: string | null
  refetch: () => void
}

export const useLearningStats = (): UseLearningStatsReturn => {
  const [stats, setStats] = useState<LearningStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const data = await statsService.getLearningStats({
        onSuccess: (data) => {
          setStats(data)
        },
        onError: (error) => {
          setError(error.message)
        },
        onLoading: (loading) => {
          setLoading(loading)
        }
      })
      
    } catch (err: any) {
      setError(err.message || 'Failed to fetch learning stats')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  }
}

// Hook for leaderboard data
interface UseLeaderboardReturn {
  leaderboard: LeaderboardResponse | null
  loading: boolean
  error: string | null
  refetch: () => void
}

export const useLeaderboard = (params: LeaderboardParams = { skip: 0, limit: 10 }): UseLeaderboardReturn => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchLeaderboard = async () => {
    try {
      setLoading(true)
      setError(null)

      const data = await statsService.getLeaderboard(params, {
        onSuccess: (data) => {
          setLeaderboard(data)
        },
        onError: (error) => {
          setError(error.message)
        },
        onLoading: (loading) => {
          setLoading(loading)
        }
      })

    } catch (err: any) {
      setError(err.message || 'Failed to fetch leaderboard')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLeaderboard()
  }, [params.skip, params.limit])

  return {
    leaderboard,
    loading,
    error,
    refetch: fetchLeaderboard
  }
}
