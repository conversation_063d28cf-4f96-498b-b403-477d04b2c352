import { useRef, useMemo } from 'react'
import { useWavesurfer } from '@wavesurfer/react'

interface UseWaveSurferPlayerOptions {
  url: string
  height?: number
  waveColor?: string
  progressColor?: string
  cursorColor?: string
  mediaControls?: boolean
  normalize?: boolean
  autoScroll?: boolean
  autoCenter?: boolean
  sampleRate?: number
  hideScrollbar?: boolean
}

/**
 * Custom hook that returns a WaveSurfer player instance for the given URL
 * Based on the reference implementation but as a reusable hook
 */
export const useWaveSurferPlayer = (options: UseWaveSurferPlayerOptions) => {
  const containerRef = useRef<HTMLDivElement>(null)

  const {
    url,
    height = 128,
    waveColor = '#e2e8f0',
    progressColor = '#dd5e98',
    cursorColor = '#ddd5e9',
    mediaControls = true,
    normalize = true,
    autoScroll = true,
    autoCenter = true,
    sampleRate = 8000,
    hideScrollbar = false
  } = options

  const { wavesurfer, isPlaying, currentTime } = useWavesurfer({
    container: containerRef,
    height,
    waveColor,
    progressColor,
    cursorColor,
    url,
    mediaControls,
    normalize,
    autoScroll,
    autoCenter,
    sampleRate,
    hideScrollbar,
    plugins: useMemo(() => [], []),
  })

  return {
    wavesurfer,
    isPlaying,
    currentTime,
    containerRef,
    duration: wavesurfer?.getDuration() || 0
  }
}

export default useWaveSurferPlayer
