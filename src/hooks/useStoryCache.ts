import { useState, useEffect, useCallback, useRef } from 'react'
import { storyService, StoryData } from '../services/story/storyService'

interface StoryCache {
  [stageKey: string]: StoryData
}

interface UseStoryCacheReturn {
  storyData: StoryData | null
  isLoading: boolean
  error: string | null
  totalStages: number
  isStageLoading: boolean
  navigateToStage: (stage: number) => void
  retryCurrentStage: () => void
}

export const useStoryCache = (storyId: string, currentStage: number): UseStoryCacheReturn => {
  const [cache, setCache] = useState<StoryCache>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isStageLoading, setIsStageLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalStages, setTotalStages] = useState(1)

  const abortControllersRef = useRef<Map<number, AbortController>>(new Map())

  // Get current stage data from cache
  const storyData = cache[`${storyId}-${currentStage}`] || null

  // Fetch stage data - only fetch what's needed
  const fetchStage = useCallback(async (stage: number) => {
    const stageKey = `${storyId}-${stage}`

    // Skip if already cached
    if (cache[stageKey]) {
      return cache[stageKey]
    }

    try {
      setIsStageLoading(true)
      setError(null)

      // Create abort controller for this request
      const abortController = new AbortController()
      abortControllersRef.current.set(stage, abortController)

      const response = await storyService.getStory(storyId, stage)

      if (response.data && response.data.length > 0) {
        const stageData = response.data[0]

        setCache(prev => ({
          ...prev,
          [stageKey]: stageData
        }))

        // Update total stages from first successful fetch
        if (totalStages === 1 && stageData.total_steps > 1) {
          setTotalStages(stageData.total_steps)
        }

        return stageData
      } else {
        throw new Error('Stage not found')
      }
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        console.error(`Error fetching stage ${stage}:`, err)
        setError(err.message || 'Failed to load stage')
      }
      return null
    } finally {
      abortControllersRef.current.delete(stage)
      setIsStageLoading(false)
    }
  }, [storyId, cache, totalStages])

  // Navigate to stage - only fetch the exact stage needed
  const navigateToStage = useCallback((stage: number) => {
    if (stage < 1 || stage > totalStages) return

    // If stage is cached, no loading needed
    const stageKey = `${storyId}-${stage}`
    if (cache[stageKey]) {
      setError(null)
      return
    }

    // Only fetch the target stage
    fetchStage(stage)
  }, [storyId, totalStages, cache, fetchStage])

  // Retry current stage
  const retryCurrentStage = useCallback(() => {
    const stageKey = `${storyId}-${currentStage}`
    setCache(prev => {
      const newCache = { ...prev }
      delete newCache[stageKey]
      return newCache
    })
    fetchStage(currentStage)
  }, [storyId, currentStage, fetchStage])

  // Initial load - only fetch the current stage
  useEffect(() => {
    if (!storyId) {
      setError('Story ID is required')
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    fetchStage(currentStage).then(() => {
      setIsLoading(false)
    })
  }, [storyId, currentStage, fetchStage])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cancel all pending requests
      abortControllersRef.current.forEach(controller => {
        controller.abort()
      })
      abortControllersRef.current.clear()
    }
  }, [])

  return {
    storyData,
    isLoading,
    error,
    totalStages,
    isStageLoading,
    navigateToStage,
    retryCurrentStage
  }
}
