import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { TaskSet, Task, TaskSetScoreResponse } from '../../services/task/taskService'

// Task state interface
interface TaskState {
  // Task sets cache
  taskSets: Record<string, TaskSet>

  // Individual task items cache
  taskItems: Record<string, Task>

  // Task questions cache (lightweight data for cards)
  taskQuestions: Record<string, { question: { text: string } }>

  // Task set scores cache
  taskSetScores: Record<string, TaskSetScoreResponse>

  // Current active task set
  currentTaskSetId: string | null

  // Loading states
  loadingTaskSet: boolean
  loadingTaskItem: boolean
  loadingQuestions: Record<string, boolean>
  loadingTaskSetScore: Record<string, boolean>

  // Error states
  error: string | null

  // Cache timestamps for invalidation
  cacheTimestamps: Record<string, number>
}

// Initial state
const initialState: TaskState = {
  taskSets: {},
  taskItems: {},
  taskQuestions: {},
  taskSetScores: {},
  currentTaskSetId: null,
  loadingTaskSet: false,
  loadingTaskItem: false,
  loadingQuestions: {},
  loadingTaskSetScore: {},
  error: null,
  cacheTimestamps: {}
}

// Task slice
const taskSlice = createSlice({
  name: 'task',
  initialState,
  reducers: {
    // Set current task set
    setCurrentTaskSetId: (state, action: PayloadAction<string | null>) => {
      state.currentTaskSetId = action.payload
    },

    // Cache task set data
    setTaskSet: (state, action: PayloadAction<{ id: string; taskSet: TaskSet }>) => {
      const { id, taskSet } = action.payload
      state.taskSets[id] = taskSet
      state.cacheTimestamps[`taskset_${id}`] = Date.now()
    },

    // Cache task item data
    setTaskItem: (state, action: PayloadAction<{ id: string; taskItem: Task }>) => {
      const { id, taskItem } = action.payload
      state.taskItems[id] = taskItem
      state.cacheTimestamps[`taskitem_${id}`] = Date.now()
    },

    // Cache task question data
    setTaskQuestion: (state, action: PayloadAction<{ id: string; question: { question: { text: string } } }>) => {
      const { id, question } = action.payload
      state.taskQuestions[id] = question
      state.cacheTimestamps[`question_${id}`] = Date.now()
    },

    // Cache task set score data
    setTaskSetScore: (state, action: PayloadAction<{ id: string; score: TaskSetScoreResponse }>) => {
      const { id, score } = action.payload
      state.taskSetScores[id] = score
      state.cacheTimestamps[`score_${id}`] = Date.now()
    },

    // Set loading states
    setLoadingTaskSet: (state, action: PayloadAction<boolean>) => {
      state.loadingTaskSet = action.payload
    },

    setLoadingTaskItem: (state, action: PayloadAction<boolean>) => {
      state.loadingTaskItem = action.payload
    },

    setLoadingQuestion: (state, action: PayloadAction<{ id: string; loading: boolean }>) => {
      const { id, loading } = action.payload
      state.loadingQuestions[id] = loading
    },

    setLoadingTaskSetScore: (state, action: PayloadAction<{ id: string; loading: boolean }>) => {
      const { id, loading } = action.payload
      state.loadingTaskSetScore[id] = loading
    },

    // Set error
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },

    // Clear error
    clearError: (state) => {
      state.error = null
    },

    // Clear cache for a specific task set
    clearTaskSetCache: (state, action: PayloadAction<string>) => {
      const taskSetId = action.payload
      delete state.taskSets[taskSetId]
      delete state.cacheTimestamps[`taskset_${taskSetId}`]
      
      // Clear related task items and questions
      Object.keys(state.taskItems).forEach(taskId => {
        delete state.taskItems[taskId]
        delete state.cacheTimestamps[`taskitem_${taskId}`]
      })
      
      Object.keys(state.taskQuestions).forEach(taskId => {
        delete state.taskQuestions[taskId]
        delete state.cacheTimestamps[`question_${taskId}`]
      })
    },

    // Clear all cache
    clearAllCache: (state) => {
      state.taskSets = {}
      state.taskItems = {}
      state.taskQuestions = {}
      state.taskSetScores = {}
      state.cacheTimestamps = {}
      state.currentTaskSetId = null
    },

    // Update task item after submission
    updateTaskItem: (state, action: PayloadAction<{ id: string; updates: Partial<Task> }>) => {
      const { id, updates } = action.payload
      if (state.taskItems[id]) {
        state.taskItems[id] = { ...state.taskItems[id], ...updates }
        state.cacheTimestamps[`taskitem_${id}`] = Date.now()
      }
    }
  }
})

// Selectors
export const selectTaskSet = (state: { task: TaskState }, taskSetId: string) => 
  state.task.taskSets[taskSetId]

export const selectTaskItem = (state: { task: TaskState }, taskItemId: string) => 
  state.task.taskItems[taskItemId]

export const selectTaskQuestion = (state: { task: TaskState }, taskId: string) => 
  state.task.taskQuestions[taskId]

export const selectCurrentTaskSet = (state: { task: TaskState }) => 
  state.task.currentTaskSetId ? state.task.taskSets[state.task.currentTaskSetId] : null

export const selectIsTaskSetCached = (state: { task: TaskState }, taskSetId: string) => 
  !!state.task.taskSets[taskSetId]

export const selectIsTaskItemCached = (state: { task: TaskState }, taskItemId: string) => 
  !!state.task.taskItems[taskItemId]

export const selectIsQuestionCached = (state: { task: TaskState }, taskId: string) =>
  !!state.task.taskQuestions[taskId]

export const selectTaskSetScore = (state: { task: TaskState }, taskSetId: string) =>
  state.task.taskSetScores[taskSetId]

export const selectIsTaskSetScoreCached = (state: { task: TaskState }, taskSetId: string) =>
  !!state.task.taskSetScores[taskSetId]

// Helper to check if cache is still valid (5 minutes)
export const selectIsCacheValid = (state: { task: TaskState }, cacheKey: string, maxAge: number = 5 * 60 * 1000) => {
  const timestamp = state.task.cacheTimestamps[cacheKey]
  return timestamp && (Date.now() - timestamp) < maxAge
}

export const {
  setCurrentTaskSetId,
  setTaskSet,
  setTaskItem,
  setTaskQuestion,
  setTaskSetScore,
  setLoadingTaskSet,
  setLoadingTaskItem,
  setLoadingQuestion,
  setLoadingTaskSetScore,
  setError,
  clearError,
  clearTaskSetCache,
  clearAllCache,
  updateTaskItem
} = taskSlice.actions

export default taskSlice.reducer
