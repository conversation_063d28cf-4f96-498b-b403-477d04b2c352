import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { User, LoginCredentials, SignupCredentials, GoogleAuthCredentials, OnboardingData, LoginResponse } from '../../types/auth'
import { authService } from '../../services/auth/authService'



// Auth state interface
interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Initial state
const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
}

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      const response = await authService.login(credentials)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message || 'Login failed')
    }
  }
)

export const signupUser = createAsyncThunk(
  'auth/signup',
  async (credentials: SignupCredentials, { rejectWithValue }) => {
    try {
      const response = await authService.signup(credentials)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message || 'Signup failed')
    }
  }
)

export const googleAuthUser = createAsyncThunk(
  'auth/googleAuth',
  async (credentials: GoogleAuthCredentials, { rejectWithValue }) => {
    try {
      const response = await authService.googleAuth(credentials)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message || 'Google authentication failed')
    }
  }
)

export const submitOnboardingData = createAsyncThunk(
  'auth/submitOnboarding',
  async (data: OnboardingData, { rejectWithValue }) => {
    try {
      const response = await authService.submitOnboarding(data)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message || 'Onboarding submission failed')
    }
  }
)

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const user = await authService.getCurrentUser()
      return user
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to get current user')
    }
  }
)

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout()
    } catch (error: any) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed, continuing with local logout')
    }
  }
)

// Helper function to convert LoginResponse to User
const loginResponseToUser = (response: LoginResponse): User => ({
  id: response.id,
  email: response.email,
  username: response.username,
  full_name: response.full_name,
  profile_picture: response.profile_picture,
  role: response.role,
  auth_provider: response.auth_provider,
  tenant_id: response.tenant_id,
  tenant_label: response.tenant_label,
  tenant_slug: response.tenant_slug,
  phone_number: response.phone_number,
  country_code: response.country_code,
  last_login: response.last_login,
  previous_login: response.previous_login,
  onboarding_completed: response.onboarding_completed,
})

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    initializeAuth: (state) => {
      const token = authService.getStoredToken()
      if (token) {
        state.token = token
        state.isAuthenticated = true
      }
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = loginResponseToUser(action.payload)
        state.token = action.payload.access_token
        state.isAuthenticated = true
        state.error = null
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
        state.isAuthenticated = false
        state.user = null
        state.token = null
      })

    // Signup
    builder
      .addCase(signupUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(signupUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.error = null
        // Auto-login after signup
        const user = loginResponseToUser(action.payload)
        state.user = user
        state.token = action.payload.access_token
        state.isAuthenticated = true
      })
      .addCase(signupUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // Google Auth
    builder
      .addCase(googleAuthUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(googleAuthUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = loginResponseToUser(action.payload)
        state.token = action.payload.access_token
        state.isAuthenticated = true
        state.error = null
      })
      .addCase(googleAuthUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
        state.isAuthenticated = false
        state.user = null
        state.token = null
      })

    // Get Current User
    builder
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload
        state.isAuthenticated = true
        state.error = null
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
        state.isAuthenticated = false
        state.user = null
        state.token = null
        localStorage.removeItem('auth_token')
      })

    // Onboarding
    builder
      .addCase(submitOnboardingData.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(submitOnboardingData.fulfilled, (state) => {
        state.isLoading = false
        if (state.user) {
          state.user.onboarding_completed = true
        }
        state.error = null
      })
      .addCase(submitOnboardingData.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // Logout
    builder
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
        state.error = null
      })
      .addCase(logoutUser.rejected, (state) => {
        // Even if logout fails, clear the state
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
        state.error = null
      })
  },
})

export const { clearError, setLoading, initializeAuth } = authSlice.actions
export default authSlice.reducer
