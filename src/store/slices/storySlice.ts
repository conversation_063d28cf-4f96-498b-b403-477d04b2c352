import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { storyService, StoryData } from '../../services/story/storyService'

// Story state interface
interface StoryState {
  // Story generation
  isGenerating: boolean
  generationError: string | null

  // Generated stories with visited flags
  stories: {
    [storyId: string]: {
      storyId: string
      status: string
      visited: boolean
      createdAt: string
    }
  }

  // Dismissed notifications (separate from visited state)
  dismissedNotifications: string[]

  // Current story data
  currentStoryData: StoryData | null
  isLoadingStory: boolean
  storyError: string | null

  // Story mode settings
  isStoryModeEnabled: boolean
  lastRecordedAudio: Blob | null
}

// Initial state
const initialState: StoryState = {
  isGenerating: false,
  generationError: null,
  stories: {},
  dismissedNotifications: [],
  currentStoryData: null,
  isLoadingStory: false,
  storyError: null,
  isStoryModeEnabled: false,
  lastRecordedAudio: null,
}

// Load persisted state from localStorage
const loadPersistedState = (): Partial<StoryState> => {
  try {
    const persistedStories = localStorage.getItem('audio-quiz-stories')
    const storyModeEnabled = localStorage.getItem('audio-quiz-story-mode-enabled')
    const dismissedNotifications = localStorage.getItem('audio-quiz-dismissed-story-notifications')

    return {
      stories: persistedStories ? JSON.parse(persistedStories) : {},
      isStoryModeEnabled: storyModeEnabled === 'true',
      dismissedNotifications: dismissedNotifications ? JSON.parse(dismissedNotifications) : [],
    }
  } catch (error) {
    console.error('Error loading persisted story state:', error)
    return {}
  }
}

// Merge persisted state with initial state
const persistedState = loadPersistedState()
const mergedInitialState: StoryState = {
  ...initialState,
  ...persistedState,
}

// Async thunks
export const generateStory = createAsyncThunk(
  'story/generateStory',
  async (audioFile: File, { rejectWithValue }) => {
    try {
      const response = await storyService.generateStory(audioFile)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to generate story')
    }
  }
)

export const fetchStoryData = createAsyncThunk(
  'story/fetchStoryData',
  async ({ storyId, stage = 1 }: { storyId: string; stage?: number }, { rejectWithValue }) => {
    try {
      const response = await storyService.getStory(storyId, stage)
      return response.data[0] // Return first story data item
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch story data')
    }
  }
)

// Story slice
const storySlice = createSlice({
  name: 'story',
  initialState: mergedInitialState,
  reducers: {
    // Story mode toggle
    toggleStoryMode: (state) => {
      state.isStoryModeEnabled = !state.isStoryModeEnabled
      localStorage.setItem('audio-quiz-story-mode-enabled', state.isStoryModeEnabled.toString())
    },
    
    setStoryModeEnabled: (state, action: PayloadAction<boolean>) => {
      state.isStoryModeEnabled = action.payload
      localStorage.setItem('audio-quiz-story-mode-enabled', action.payload.toString())
    },
    
    // Audio recording storage
    setLastRecordedAudio: (state, action: PayloadAction<Blob | null>) => {
      state.lastRecordedAudio = action.payload
    },
    
    // Story visited flag
    markStoryAsVisited: (state, action: PayloadAction<string>) => {
      const storyId = action.payload
      if (state.stories[storyId]) {
        state.stories[storyId].visited = true
        localStorage.setItem('audio-quiz-stories', JSON.stringify(state.stories))
      }
    },

    // Dismiss notification temporarily (separate from visited state)
    dismissStoryNotification: (state, action: PayloadAction<string>) => {
      const storyId = action.payload
      if (!state.dismissedNotifications.includes(storyId)) {
        state.dismissedNotifications.push(storyId)
        localStorage.setItem('audio-quiz-dismissed-story-notifications', JSON.stringify(state.dismissedNotifications))
      }
    },

    // Remove from dismissed list (when story is visited)
    undismissStoryNotification: (state, action: PayloadAction<string>) => {
      const storyId = action.payload
      state.dismissedNotifications = state.dismissedNotifications.filter(id => id !== storyId)
      localStorage.setItem('audio-quiz-dismissed-story-notifications', JSON.stringify(state.dismissedNotifications))
    },
    
    // Clear errors
    clearGenerationError: (state) => {
      state.generationError = null
    },
    
    clearStoryError: (state) => {
      state.storyError = null
    },
    
    // Reset story data
    resetCurrentStory: (state) => {
      state.currentStoryData = null
      state.storyError = null
    },
  },
  extraReducers: (builder) => {
    // Generate story
    builder
      .addCase(generateStory.pending, (state) => {
        state.isGenerating = true
        state.generationError = null
      })
      .addCase(generateStory.fulfilled, (state, action) => {
        state.isGenerating = false
        const { story_id, status } = action.payload

        // Add to stories with visited flag false
        state.stories[story_id] = {
          storyId: story_id,
          status,
          visited: false,
          createdAt: new Date().toISOString(),
        }

        // Persist to localStorage
        localStorage.setItem('audio-quiz-stories', JSON.stringify(state.stories))

        // Clear any dismissed notifications for this story since it's new
        state.dismissedNotifications = state.dismissedNotifications.filter(id => id !== story_id)
        localStorage.setItem('audio-quiz-dismissed-story-notifications', JSON.stringify(state.dismissedNotifications))
      })
      .addCase(generateStory.rejected, (state, action) => {
        state.isGenerating = false
        state.generationError = action.payload as string
      })
    
    // Fetch story data
    builder
      .addCase(fetchStoryData.pending, (state) => {
        state.isLoadingStory = true
        state.storyError = null
      })
      .addCase(fetchStoryData.fulfilled, (state, action) => {
        state.isLoadingStory = false
        state.currentStoryData = action.payload
      })
      .addCase(fetchStoryData.rejected, (state, action) => {
        state.isLoadingStory = false
        state.storyError = action.payload as string
      })
  },
})

export const {
  toggleStoryMode,
  setStoryModeEnabled,
  setLastRecordedAudio,
  markStoryAsVisited,
  dismissStoryNotification,
  undismissStoryNotification,
  clearGenerationError,
  clearStoryError,
  resetCurrentStory,
} = storySlice.actions

export default storySlice.reducer
