import React from 'react'
import <PERSON>act<PERSON><PERSON> from 'react-dom/client'
import { GoogleOAuthProvider } from '@react-oauth/google'
import App from './App.tsx'
import './styles/index.css'

// Get Google client ID from environment variables
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || ''

// Log the client ID for debugging
console.log('Google Client ID:', GOOGLE_CLIENT_ID)
console.log('Client ID length:', GOOGLE_CLIENT_ID.length)
console.log('Is valid client ID format:', GOOGLE_CLIENT_ID.endsWith('.apps.googleusercontent.com'))

// Validate client ID format
if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_ID.endsWith('.apps.googleusercontent.com')) {
  console.error('Invalid Google Client ID format. Please check your .env file.')
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
      <App />
    </GoogleOAuthProvider>
  </React.StrictMode>,
)
