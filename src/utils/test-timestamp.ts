/**
 * Test file to verify timestamp conversion
 * This can be run in the browser console to test the fix
 */

import { formatTimeAgo, utcToLocal } from './dateTimeHelper'

// Test function to verify timestamp conversion
export const testTimestampConversion = () => {
  console.log('=== Testing Timestamp Conversion ===')
  
  // Simulate current time
  const now = new Date()
  console.log('Current local time:', now.toLocaleString())
  console.log('Current UTC time:', now.toISOString())
  
  // Test with a timestamp that's 1 hour ago in UTC (without Z)
  const oneHourAgoUTC = new Date(now.getTime() - (60 * 60 * 1000))
  const utcStringWithoutZ = oneHourAgoUTC.toISOString().slice(0, -1) // Remove 'Z'
  const utcStringWithZ = oneHourAgoUTC.toISOString() // Keep 'Z'
  
  console.log('\n--- Test 1: UTC string without Z (backend format) ---')
  console.log('UTC string (no Z):', utcStringWithoutZ)
  console.log('Converted to local:', utcToLocal(utcStringWithoutZ).toLocaleString())
  console.log('Time ago:', formatTimeAgo(utcStringWithoutZ))
  
  console.log('\n--- Test 2: UTC string with Z ---')
  console.log('UTC string (with Z):', utcStringWithZ)
  console.log('Converted to local:', utcToLocal(utcStringWithZ).toLocaleString())
  console.log('Time ago:', formatTimeAgo(utcStringWithZ))
  
  // Test with a timestamp that's 5 minutes ago
  const fiveMinutesAgoUTC = new Date(now.getTime() - (5 * 60 * 1000))
  const fiveMinutesAgoString = fiveMinutesAgoUTC.toISOString().slice(0, -1)
  
  console.log('\n--- Test 3: 5 minutes ago (should show "5m ago") ---')
  console.log('UTC string:', fiveMinutesAgoString)
  console.log('Time ago:', formatTimeAgo(fiveMinutesAgoString))
  
  // Test with a timestamp that's just created (should show "Just now")
  const justNowString = now.toISOString().slice(0, -1)
  
  console.log('\n--- Test 4: Just now (should show "Just now") ---')
  console.log('UTC string:', justNowString)
  console.log('Time ago:', formatTimeAgo(justNowString))
}

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).testTimestampConversion = testTimestampConversion
}
