/**
 * Responsive Design Test Utilities
 * 
 * This file contains utilities to test responsive design implementation
 * across different viewport sizes for the Audio Quiz Genie Kids app.
 */

export interface ViewportSize {
  name: string
  width: number
  height: number
  category: 'mobile' | 'tablet' | 'desktop'
}

export const VIEWPORT_SIZES: ViewportSize[] = [
  // Mobile phones (320px - 768px)
  { name: 'iPhone SE', width: 375, height: 667, category: 'mobile' },
  { name: 'iPhone 12', width: 390, height: 844, category: 'mobile' },
  { name: 'Samsung Galaxy S21', width: 360, height: 800, category: 'mobile' },
  { name: 'Small Mobile', width: 320, height: 568, category: 'mobile' },
  
  // Tablets/iPads (768px - 1024px)
  { name: 'iPad Mini', width: 768, height: 1024, category: 'tablet' },
  { name: 'iPad Air', width: 820, height: 1180, category: 'tablet' },
  { name: 'iPad Pro 11"', width: 834, height: 1194, category: 'tablet' },
  { name: 'Surface Pro', width: 912, height: 1368, category: 'tablet' },
  
  // Laptops/Desktops (1024px+)
  { name: 'Laptop Small', width: 1024, height: 768, category: 'desktop' },
  { name: 'Laptop Medium', width: 1366, height: 768, category: 'desktop' },
  { name: 'Desktop', width: 1920, height: 1080, category: 'desktop' },
  { name: 'Large Desktop', width: 2560, height: 1440, category: 'desktop' },
]

export interface ResponsiveTestResult {
  viewport: ViewportSize
  passed: boolean
  issues: string[]
  recommendations: string[]
}

/**
 * Test responsive design implementation
 */
export class ResponsiveDesignTester {
  private static instance: ResponsiveDesignTester
  
  public static getInstance(): ResponsiveDesignTester {
    if (!ResponsiveDesignTester.instance) {
      ResponsiveDesignTester.instance = new ResponsiveDesignTester()
    }
    return ResponsiveDesignTester.instance
  }

  /**
   * Test a specific viewport size
   */
  public testViewport(viewport: ViewportSize): ResponsiveTestResult {
    const issues: string[] = []
    const recommendations: string[] = []

    // Set viewport size
    if (typeof window !== 'undefined') {
      // Simulate viewport resize
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: viewport.width,
      })
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: viewport.height,
      })
      
      // Trigger resize event
      window.dispatchEvent(new Event('resize'))
    }

    // Test touch targets for mobile
    if (viewport.category === 'mobile') {
      this.testTouchTargets(issues, recommendations)
    }

    // Test text readability
    this.testTextReadability(viewport, issues, recommendations)

    // Test layout overflow
    this.testLayoutOverflow(viewport, issues, recommendations)

    // Test navigation accessibility
    this.testNavigationAccessibility(viewport, issues, recommendations)

    return {
      viewport,
      passed: issues.length === 0,
      issues,
      recommendations
    }
  }

  /**
   * Test all viewport sizes
   */
  public testAllViewports(): ResponsiveTestResult[] {
    return VIEWPORT_SIZES.map(viewport => this.testViewport(viewport))
  }

  private testTouchTargets(issues: string[], recommendations: string[]): void {
    if (typeof document === 'undefined') return

    const buttons = document.querySelectorAll('button, a, input[type="button"], input[type="submit"]')
    
    buttons.forEach((element, index) => {
      const rect = element.getBoundingClientRect()
      const minSize = 44 // Minimum touch target size in pixels
      
      if (rect.width < minSize || rect.height < minSize) {
        issues.push(`Touch target ${index + 1} is too small: ${rect.width}x${rect.height}px (minimum: ${minSize}x${minSize}px)`)
        recommendations.push(`Add 'touch-target' class or increase padding for element ${index + 1}`)
      }
    })
  }

  private testTextReadability(viewport: ViewportSize, issues: string[], recommendations: string[]): void {
    if (typeof document === 'undefined') return

    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6')
    
    textElements.forEach((element, index) => {
      const styles = window.getComputedStyle(element)
      const fontSize = parseFloat(styles.fontSize)
      
      // Minimum font sizes based on viewport
      let minFontSize = 14
      if (viewport.category === 'mobile') minFontSize = 14
      else if (viewport.category === 'tablet') minFontSize = 15
      else minFontSize = 16
      
      if (fontSize < minFontSize) {
        issues.push(`Text element ${index + 1} font size is too small: ${fontSize}px (minimum: ${minFontSize}px for ${viewport.category})`)
        recommendations.push(`Use responsive text classes like 'text-sm sm:text-base' for element ${index + 1}`)
      }
    })
  }

  private testLayoutOverflow(viewport: ViewportSize, issues: string[], recommendations: string[]): void {
    if (typeof document === 'undefined') return

    const containers = document.querySelectorAll('div, section, main')
    
    containers.forEach((element, index) => {
      const rect = element.getBoundingClientRect()
      
      if (rect.width > viewport.width) {
        issues.push(`Container ${index + 1} overflows viewport: ${rect.width}px > ${viewport.width}px`)
        recommendations.push(`Add responsive width classes or max-width constraints to container ${index + 1}`)
      }
    })
  }

  private testNavigationAccessibility(viewport: ViewportSize, issues: string[], recommendations: string[]): void {
    if (typeof document === 'undefined') return

    // Test mobile navigation
    if (viewport.category === 'mobile') {
      const mobileNav = document.querySelector('[data-mobile-nav]')
      const hamburgerButton = document.querySelector('[data-mobile-menu-toggle]')
      
      if (!hamburgerButton) {
        issues.push('Mobile hamburger menu button not found')
        recommendations.push('Add mobile menu toggle button for navigation')
      }
      
      if (!mobileNav) {
        issues.push('Mobile navigation menu not found')
        recommendations.push('Implement collapsible mobile navigation')
      }
    }
  }

  /**
   * Generate a comprehensive test report
   */
  public generateReport(): string {
    const results = this.testAllViewports()
    const passedTests = results.filter(r => r.passed).length
    const totalTests = results.length
    
    let report = `# Responsive Design Test Report\n\n`
    report += `**Overall Score: ${passedTests}/${totalTests} (${Math.round((passedTests/totalTests) * 100)}%)**\n\n`
    
    // Group by category
    const categories = ['mobile', 'tablet', 'desktop'] as const
    
    categories.forEach(category => {
      const categoryResults = results.filter(r => r.viewport.category === category)
      const categoryPassed = categoryResults.filter(r => r.passed).length
      
      report += `## ${category.charAt(0).toUpperCase() + category.slice(1)} Devices (${categoryPassed}/${categoryResults.length})\n\n`
      
      categoryResults.forEach(result => {
        const status = result.passed ? '✅' : '❌'
        report += `### ${status} ${result.viewport.name} (${result.viewport.width}x${result.viewport.height})\n\n`
        
        if (result.issues.length > 0) {
          report += `**Issues:**\n`
          result.issues.forEach(issue => report += `- ${issue}\n`)
          report += `\n`
        }
        
        if (result.recommendations.length > 0) {
          report += `**Recommendations:**\n`
          result.recommendations.forEach(rec => report += `- ${rec}\n`)
          report += `\n`
        }
      })
    })
    
    return report
  }
}

// Export singleton instance
export const responsiveTester = ResponsiveDesignTester.getInstance()
