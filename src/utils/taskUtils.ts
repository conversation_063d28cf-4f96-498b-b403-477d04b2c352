/**
 * Formats a task type for display
 * @param type The raw task type from the API
 * @returns Formatted display name for the task type
 */
export const formatTaskType = (type: string): { display: string; tooltip: string } => {
  const typeMap: Record<string, { display: string; tooltip: string }> = {
    'speak_word': {
      display: 'Speak',
      tooltip: 'Speak Word - Record your pronunciation of the given word'
    },
    'multiple_choice': {
      display: 'MCQ',
      tooltip: 'Multiple Choice - Select the correct answer from the given options'
    },
    'fill_blank': {
      display: 'Fill',
      tooltip: 'Fill in the Blank - Complete the sentence with the missing word(s)'
    },
    'true_false': {
      display: 'T/F',
      tooltip: 'True or False - Determine if the statement is true or false'
    },
    'matching': {
      display: 'Match',
      tooltip: 'Matching - Match items from one column to another'
    },
    'ordering': {
      display: 'Order',
      tooltip: 'Ordering - Arrange items in the correct sequence'
    },
    'short_answer': {
      display: 'Short',
      tooltip: 'Short Answer - Provide a brief written response'
    },
    'essay': {
      display: 'Essay',
      tooltip: 'Essay - Provide a detailed written response'
    }
  }

  // Return the formatted type if found, otherwise return the original type
  return typeMap[type] || { 
    display: type,
    tooltip: type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
  }
}
