import httpBase, { RequestCallbacks } from '../http/httpBase'

// Learning stats interface based on the API response
export interface LearningStats {
  total_sets: number
  total_tasks: number
  total_attempted_tasks: number
  accuracy_rate: number
  total_possible_score: number
  total_scored: number
}

// Leaderboard interfaces
export interface LeaderboardEntry {
  user_id: string
  username: string
  profile_picture?: string
  total_score: number
  accuracy: number
  total_attempts: number
  rank: number
}

export interface LeaderboardResponse {
  data: LeaderboardEntry[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface LeaderboardParams {
  skip?: number
  limit?: number
}

class StatsService {
  // Get user learning statistics
  async getLearningStats(
    callbacks?: RequestCallbacks<LearningStats>
  ): Promise<LearningStats> {
    const response = await httpBase.get<LearningStats>(
      '/management/scoring/user/learning_stats',
      {},
      callbacks
    )

    return response.data
  }

  // Get leaderboard data
  async getLeaderboard(
    params: LeaderboardParams = {},
    callbacks?: RequestCallbacks<LeaderboardResponse>
  ): Promise<LeaderboardResponse> {
    const queryParams = new URLSearchParams()

    if (params.skip !== undefined) queryParams.append('skip', params.skip.toString())
    if (params.limit !== undefined) queryParams.append('limit', params.limit.toString())

    const response = await httpBase.get<LeaderboardResponse>(
      `/management/scoring/leaderboard?${queryParams.toString()}`,
      {},
      callbacks
    )

    return response.data
  }
}

// Export singleton instance
export const statsService = new StatsService()
export default statsService
