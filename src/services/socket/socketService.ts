import { io, Socket } from 'socket.io-client'
import { authService } from '../auth/authService'
import axios from 'axios'

export interface SocketSession {
  session_token: string
  session_id: string
  websocket_url: string
  expires_at: string
  configuration: {
    difficulty: string
    num_tasks: number
    chunk_threshold: number
  }
  status: string
  instructions: {
    next_step: string
    websocket_endpoint: string
    auth_method: string
    flow: Record<string, string>
    events: Record<string, string>
  }
}

export interface SocketConnectionState {
  status: 'DISCONNECTED' | 'CONNECTING' | 'CONNECTED' | 'ACTIVE' | 'COMPLETED' | 'ERROR' | 'CANCELLED'
  session: SocketSession | null
  error: string | null
}

class SocketService {
  private socket: Socket | null = null
  private session: SocketSession | null = null
  private eventHandlers: Record<string, Array<(data: any) => void>> = {}
  private socketHttpClient: any

  constructor() {
    // Create a custom HTTP client for socket service that uses v2 API
    const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8204/v1'
    const v2BaseURL = baseURL.replace(/\/v1$/, '/v2')

    this.socketHttpClient = axios.create({
      baseURL: v2BaseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add request interceptor for auth token
    this.socketHttpClient.interceptors.request.use(
      (config: any) => {
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        console.log(`🚀 Socket Service ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error: any) => {
        console.error('❌ Socket Service Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Add response interceptor
    this.socketHttpClient.interceptors.response.use(
      (response: any) => {
        console.log(`✅ Socket Service ${response.status} ${response.config.url}`)
        return response
      },
      (error: any) => {
        console.error(`❌ Socket Service ${error.response?.status || 'Network'} ${error.config?.url}`)
        return Promise.reject(error)
      }
    )
  }

  // Step 1: HTTP Authentication - Create session via HTTP
  async authenticateSession(_config: {
    difficulty?: 'easy' | 'medium' | 'hard'
    num_tasks?: number
    chunk_threshold?: number
  } = {}): Promise<SocketSession> {
    try {
      console.log('🔐 Starting HTTP authentication for Socket.IO connection')

      // Get auth token
      const token = authService.getStoredToken()
      if (!token) {
        throw new Error('No authentication token available')
      }

      // Create session via HTTP using v2 API
      const response = await this.socketHttpClient.post('/socket/connect', {
        // difficulty: difficultyInt,
        // num_tasks: config.num_tasks || 3,
        // chunk_threshold: config.chunk_threshold || 20,
        // metadata: {
        //   timestamp: new Date().toISOString(),
        //   client_type: 'web',
        //   user_agent: navigator.userAgent
        // }
      })

      // Validate response
      if (!response.data.session_token || !response.data.session_id || !response.data.websocket_url) {
        throw new Error('Invalid authentication response: missing required fields')
      }

      this.session = response.data

      console.log('✅ HTTP authentication successful')
      console.log(`📋 Session ID: ${this.session.session_id}`)
      console.log(`🔗 WebSocket URL: ${this.session.websocket_url}`)
      console.log(`📊 Status: ${this.session.status}`)

      return response.data
    } catch (error: any) {
      console.error('❌ HTTP authentication failed:', error)
      throw error
    }
  }

  // Step 2: WebSocket Connection - Connect using session credentials
  async connectWebSocket(session?: SocketSession): Promise<void> {
    const sessionToUse = session || this.session
    if (!sessionToUse) {
      throw new Error('No session available. Authenticate first.')
    }

    console.log('🔗 Starting WebSocket connection')

    // Construct full WebSocket URL using v2 API
    const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8204/v1'
    const cleanBaseUrl = baseUrl.replace(/\/v1$/, '')
    const fullWebSocketUrl = `${cleanBaseUrl}${sessionToUse.websocket_url.replace('/v1/', '/v2/')}`

    console.log(`🔗 Connecting to WebSocket: ${fullWebSocketUrl}`)

    return this.connect(sessionToUse, fullWebSocketUrl)
  }

  // Combined method for convenience
  async createSessionAndConnect(config: {
    difficulty?: 'easy' | 'medium' | 'hard'
    num_tasks?: number
    chunk_threshold?: number
  } = {}): Promise<SocketSession> {
    try {
      // Step 1: HTTP Authentication
      const session = await this.authenticateSession(config)

      // Step 2: WebSocket Connection
      await this.connectWebSocket(session)

      return session
    } catch (error: any) {
      console.error('Failed to create session and connect:', error)
      throw error
    }
  }

  // Connect to socket with session and optional URL
  async connect(session?: SocketSession, websocketUrl?: string): Promise<void> {
    if (!session && !this.session) {
      throw new Error('No session available. Create a session first.')
    }

    const sessionToUse = session || this.session!

    // Disconnect existing socket
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }

    // Use provided URL or construct from session using v2 API
    let fullWebSocketUrl = websocketUrl
    if (!fullWebSocketUrl) {
      const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8204'
      const cleanBaseUrl = baseUrl.replace(/\/v1$/, '')
      fullWebSocketUrl = `${cleanBaseUrl}${sessionToUse.websocket_url.replace('/v1/', '/v2/')}`
    }

    // Parse URL for socket.io
    const urlObj = new URL(fullWebSocketUrl)
    const socketBaseUrl = `${urlObj.protocol}//${urlObj.host}`
    const path = urlObj.pathname

    return new Promise((resolve, reject) => {
      this.socket = io(socketBaseUrl, {
        path: path,
        auth: {
          session_token: sessionToUse.session_token
        },
        transports: ['polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: 3,
        reconnectionDelay: 1000,
        forceNew: true,
        autoConnect: false,
        upgrade: false,
        rememberUpgrade: true
      })

      this.socket.connect()

      this.socket.on('connect', () => {
        console.log('🔗 Socket connected')
        this.setupEventHandlers()
        this.triggerEvent('state_change', { status: 'CONNECTED' })
        resolve()
      })

      this.socket.on('connect_error', (error) => {
        console.error('❌ Socket connection error:', error)
        this.triggerEvent('state_change', { status: 'ERROR', error: error.message })
        reject(new Error(`Socket connection failed: ${error.message}`))
      })

      this.socket.on('disconnect', (reason) => {
        console.log('🔌 Socket disconnected:', reason)
        this.triggerEvent('state_change', { status: 'DISCONNECTED' })
        this.triggerEvent('disconnect', { reason })
      })

      // Connection timeout
      setTimeout(() => {
        if (!this.socket?.connected) {
          reject(new Error('Socket connection timeout'))
        }
      }, 10000)
    })
  }

  // Setup event handlers
  private setupEventHandlers(): void {
    if (!this.socket) return

    // Stream acknowledgments
    this.socket.on('stream_starting_ack', (data: any) => {
      console.log('📡 Received stream_starting_ack:', data)
      this.triggerEvent('state_change', { status: 'ACTIVE' })
      this.triggerEvent('stream_starting_ack', data)
    })

    this.socket.on('stream_completed_ack', (data: any) => {
      console.log('📡 Received stream_completed_ack:', data)
      this.triggerEvent('state_change', { status: 'COMPLETED' })
      this.triggerEvent('stream_completed_ack', data)
    })

    this.socket.on('stream_stop_ack', (data: any) => {
      console.log('📡 Received stream_stop_ack:', data)
      this.triggerEvent('state_change', { status: 'CANCELLED' })
      this.triggerEvent('stream_stop_ack', data)
    })

    // Task generation events
    this.socket.on('task_generation_processing', (data: any) => {
      console.log('📡 Received task_generation_processing:', data)
      this.triggerEvent('task_generation_processing', data)
    })

    this.socket.on('task_generation_complete', (data: any) => {
      console.log('📡 Received task_generation_complete:', data)
      console.log('🎯 Task Set ID:', data.task_set_id)
      this.triggerEvent('state_change', { status: 'COMPLETED' })
      this.triggerEvent('task_generation_complete', data)
    })

    this.socket.on('task_generation_failed', (data: any) => {
      console.error('📡 Received task_generation_failed:', data)
      this.triggerEvent('state_change', { status: 'ERROR', error: data.error })
      this.triggerEvent('task_generation_failed', data)
    })

    // Chunk acknowledgment
    this.socket.on('chunk_received', (data: any) => {
      console.log('✅ Chunk received acknowledgment:', data)
      this.triggerEvent('chunk_received', data)
    })

    // Error handling
    this.socket.on('error', (data: any) => {
      console.error('❌ Server error:', data)
      this.triggerEvent('state_change', { status: 'ERROR', error: data.message })
      this.triggerEvent('error', data)
    })
  }

  // Start streaming
  async startStreaming(): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected')
    }

    if (!this.session) {
      throw new Error('No session available')
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Stream starting timeout'))
      }, 5000)

      const handleStreamStartingAck = () => {
        clearTimeout(timeout)
        this.socket?.off('stream_starting_ack', handleStreamStartingAck)
        resolve()
      }

      this.socket!.on('stream_starting_ack', handleStreamStartingAck)
      this.socket!.emit('stream_starting', { session_id: this.session.session_id })
    })
  }

  // Send audio chunk according to API specification
  sendAudioChunk(audioData: ArrayBuffer, chunkIndex: number): void {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected')
    }

    if (!this.session) {
      throw new Error('No session available')
    }

    console.log(`📤 Sending binary chunk #${chunkIndex}: ${audioData.byteLength} bytes`)

    // Convert ArrayBuffer to Uint8Array as expected by the server
    const uint8Array = new Uint8Array(audioData)

    // Create payload object with all data and metadata as expected by server
    const payload = {
      session_id: this.session.session_id,
      chunk_id: chunkIndex,
      audio_data: uint8Array,
      metadata: {
        timestamp: new Date().toISOString(),
        chunk_size: audioData.byteLength,
        sequence_number: chunkIndex
      }
    }

    console.log(`📤 Sending binary payload:`, {
      session_id: payload.session_id,
      chunk_id: payload.chunk_id,
      audio_data_length: payload.audio_data.length,
      metadata: payload.metadata
    })

    // Send complete payload object as per API spec
    this.socket.emit('binary_data', payload)
  }

  // Complete streaming - this triggers task generation but keeps socket alive
  async completeStreaming(): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected')
    }

    if (!this.session) {
      throw new Error('No session available')
    }

    const payload = { session_id: this.session.session_id }
    console.log('🏁 Sending stream_completed event with payload:', payload)

    // Emit the event - this will trigger task generation on the server
    this.socket.emit('stream_completed', payload)

    // Update state but keep socket connected to listen for task_generation_complete
    this.triggerEvent('state_change', { status: 'COMPLETED' })

    console.log('✅ Stream completed, socket remains connected for task generation events')
  }

  // Stop streaming
  async stopStreaming(): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected')
    }

    if (!this.session) {
      throw new Error('No session available')
    }

    const payload = { session_id: this.session.session_id }
    console.log('🛑 Sending stream_stop event with payload:', payload)
    this.socket.emit('stream_stop', payload)
  }

  // Wait for task generation to complete (optional helper method)
  async waitForTaskGeneration(timeoutMs: number = 300000): Promise<string> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Task generation timeout'))
      }, timeoutMs)

      const handleTaskGenerationComplete = (data: any) => {
        clearTimeout(timeout)
        this.off('task_generation_complete', handleTaskGenerationComplete)
        if (data.task_set_id) {
          resolve(data.task_set_id)
        } else {
          reject(new Error('No task_set_id received'))
        }
      }

      const handleTaskGenerationFailed = (data: any) => {
        clearTimeout(timeout)
        this.off('task_generation_complete', handleTaskGenerationComplete)
        this.off('task_generation_failed', handleTaskGenerationFailed)
        reject(new Error(data.error || 'Task generation failed'))
      }

      this.on('task_generation_complete', handleTaskGenerationComplete)
      this.on('task_generation_failed', handleTaskGenerationFailed)
    })
  }

  // Disconnect
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.session = null
    this.eventHandlers = {}
  }

  // Event handling
  on(event: string, handler: (data: any) => void): void {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = []
    }
    this.eventHandlers[event].push(handler)
  }

  off(event: string, handler?: (data: any) => void): void {
    if (!this.eventHandlers[event]) {
      return
    }

    if (!handler) {
      delete this.eventHandlers[event]
    } else {
      this.eventHandlers[event] = this.eventHandlers[event].filter(h => h !== handler)
    }
  }

  private triggerEvent(event: string, data: any = null): void {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in ${event} handler:`, error)
        }
      })
    }
  }

  // Getters
  isConnected(): boolean {
    return this.socket?.connected === true
  }

  getSession(): SocketSession | null {
    return this.session
  }
}

export const socketService = new SocketService()
export default socketService
