import httpBase, { RequestCallbacks } from '../http/httpBase'

// Interface for single story response - ONLY INTERFACE NEEDED
export interface SingleStoryResponse {
  steps?: Array<{
    stage: number
    script: string
    image?: string
    audio_url?: string
  }>
  total_steps: number
  completed_steps: number
  status: string
  created_at: string
  updated_at: string
  script: string
  task_title: string
  audio_metadata?:{name: string
    bucket_name: string
    object_path: string
    file_name: string
    url: string
    content_type: string
    size_bytes: number
    user_id: string
    folder: string
    session_id: string | null
    created_at: string
    file_extension: string
    _image_ready: boolean
    _priority: string
  },
  metadata: {
    object_name: string
    bucket_name: string
    object_path: string
    file_name: string
    url: string
    content_type: string
    size_bytes: number
    user_id: string
    folder: string
    session_id: string | null
    created_at: string
    file_extension: string
    _image_ready: boolean
    _priority: string
  }
  id: string
}

class StoryService {
  // Get single story with specific fields - ONLY METHOD NEEDED
  // Matches the curl: GET /management/story/{id}?fields=steps&fields=total_steps&fields=completed_steps&fields=status&fields=created_at&fields=updated_at&fields=metadata&fields=script&fields=task_title
  async getSingleStory(
    storyId: string,
    // fields: string[] = ['steps', 'total_steps', 'completed_steps', 'status', 'created_at', 'updated_at', 'metadata', 'script', 'task_title'],
    callbacks?: RequestCallbacks<SingleStoryResponse>
  ): Promise<SingleStoryResponse> {
    // Build query string with multiple fields parameters exactly like the curl
    // const fieldsQuery = fields.map(field => `fields=${field}`).join('&')

    const response = await httpBase.get<SingleStoryResponse>(
      `/management/story/${storyId}`,
      {},
      callbacks
    )

    return response.data
  }
}

// Export singleton instance
export const storyService = new StoryService()
export default storyService
