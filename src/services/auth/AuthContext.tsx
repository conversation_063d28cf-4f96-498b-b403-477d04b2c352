import React, { createContext, useContext, useReducer, useEffect } from 'react'
import {
  AuthState,
  AuthContextType,
  LoginCredentials,
  SignupCredentials,
  GoogleAuthCredentials,
  OnboardingData,
  User,
  LoginResponse
} from '../../types/auth'
import authService from './authService'

// Helper function to convert LoginResponse to User
function loginResponseToUser(response: LoginResponse): User {
  return {
    id: response.id,
    email: response.email,
    username: response.username,
    full_name: response.full_name,
    profile_picture: response.profile_picture,
    role: response.role,
    auth_provider: response.auth_provider,
    tenant_id: response.tenant_id,
    tenant_label: response.tenant_label,
    tenant_slug: response.tenant_slug,
    phone_number: response.phone_number,
    country_code: response.country_code,
    last_login: response.last_login,
    previous_login: response.previous_login,
    onboarding_completed: response.onboarding_completed
  }
}

// Auth Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_LOADING'; payload: boolean }

// Initial state
const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
}

// Auth reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      }

    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      }

    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      }

    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      }

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      }

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      }

    default:
      return state
  }
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth Provider
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Initialize auth state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      const token = authService.getStoredToken()
      
      if (token) {
        dispatch({ type: 'SET_LOADING', payload: true })
        
        try {
          const user = await authService.getCurrentUser({
            onError: () => {
              // Token is invalid, clear it
              authService.logout()
              dispatch({ type: 'LOGOUT' })
            }
          })
          
          dispatch({
            type: 'AUTH_SUCCESS',
            payload: { user, token }
          })
        } catch (error) {
          dispatch({ type: 'LOGOUT' })
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false })
        }
      }
    }

    initializeAuth()
  }, [])

  // Login function
  const login = async (credentials: LoginCredentials) => {
    dispatch({ type: 'AUTH_START' })

    try {
      const response = await authService.login(credentials, {
        onError: (error) => {
          dispatch({ type: 'AUTH_ERROR', payload: error.message })
        }
      })

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: loginResponseToUser(response),
          token: response.access_token
        }
      })
    } catch (error: any) {
      dispatch({ type: 'AUTH_ERROR', payload: error.message })
    }
  }

  // Signup function
  const signup = async (credentials: SignupCredentials) => {
    dispatch({ type: 'AUTH_START' })

    try {
      const response = await authService.signup(credentials, {
        onError: (error) => {
          dispatch({ type: 'AUTH_ERROR', payload: error.message })
        }
      })

      // Auto-login after signup (response now has same format as login)
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: loginResponseToUser(response),
          token: response.access_token
        }
      })
    } catch (error: any) {
      dispatch({ type: 'AUTH_ERROR', payload: error.message })
    }
  }

  // Google authentication function
  const googleAuth = async (credentials: GoogleAuthCredentials) => {
    dispatch({ type: 'AUTH_START' })

    try {
      const response = await authService.googleAuth(credentials, {
        onError: (error) => {
          dispatch({ type: 'AUTH_ERROR', payload: error.message })
        }
      })

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: loginResponseToUser(response),
          token: response.access_token
        }
      })
    } catch (error: any) {
      dispatch({ type: 'AUTH_ERROR', payload: error.message })
    }
  }

  // Submit onboarding function
  const submitOnboarding = async (data: OnboardingData) => {
    dispatch({ type: 'SET_LOADING', payload: true })

    try {
      await authService.submitOnboarding(data, {
        onError: (error) => {
          dispatch({ type: 'AUTH_ERROR', payload: error.message })
        }
      })

      // Update user to mark onboarding as completed
      if (state.user) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: { ...state.user, onboarding_completed: true },
            token: state.token || ''
          }
        })
      }
    } catch (error: any) {
      dispatch({ type: 'AUTH_ERROR', payload: error.message })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  // Logout function
  const logout = async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    
    try {
      await authService.logout()
    } catch (error) {
      console.warn('Logout error:', error)
    } finally {
      dispatch({ type: 'LOGOUT' })
    }
  }

  // Clear error function
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  const value: AuthContextType = {
    ...state,
    login,
    signup,
    googleAuth,
    submitOnboarding,
    logout,
    clearError,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
