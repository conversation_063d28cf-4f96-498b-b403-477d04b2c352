@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 262.1 83.3% 57.8%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 262.1 83.3% 57.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262.1 83.3% 57.8%;
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 262.1 83.3% 57.8%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 262.1 83.3% 57.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Hide browser navigation progress bar */
  ::-webkit-progress-bar {
    display: none !important;
  }

  /* Hide Chrome's navigation progress indicator */
  html {
    --webkit-progress-bar-display: none !important;
  }
}

/* YouTube-style skeleton animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes shimmer-slow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes heartbeat {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

@keyframes heartbeat-slow {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.01);
  }
}

@layer utilities {
  .animate-shimmer {
    animation: shimmer 1.5s ease-in-out infinite;
  }

  .animate-shimmer-slow {
    animation: shimmer-slow 2s ease-in-out infinite;
  }

  .animate-heartbeat {
    animation: heartbeat 1.2s ease-in-out infinite;
  }

  .animate-heartbeat-slow {
    animation: heartbeat-slow 2s ease-in-out infinite;
  }

  /* Google Sign-in Button Styling - Full width with proper responsive behavior */
  .google-login-wrapper {
    width: 100% !important;
    display: block !important;
  }

  .google-login-wrapper > div {
    width: 100% !important;
    max-width: 100% !important;
  }

  .google-login-wrapper div[role="button"] {
    border-radius: 1rem !important;
    font-family: inherit !important;
    font-weight: 500 !important;
    padding: 12px 16px !important;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    box-sizing: border-box !important;
  }

  /* Ensure the iframe inside also takes full width */
  .google-login-wrapper iframe {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Responsive utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  @media (max-width: 768px) {
    .touch-target {
      min-height: 48px;
      min-width: 48px;
    }
  }

  /* Ensure proper text scaling on mobile */
  @media (max-width: 640px) {
    html {
      font-size: 14px;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    html {
      font-size: 15px;
    }
  }

  @media (min-width: 1025px) {
    html {
      font-size: 16px;
    }
  }

  /* Hide any browser loading indicators */
  progress {
    display: none !important;
  }

  /* Hide navigation loading bars */
  .nprogress {
    display: none !important;
  }

  /* Hide any router loading indicators */
  [data-loading-bar] {
    display: none !important;
  }
}
