import React from 'react'
import { cn } from '../../utils/cn'
import SideNavigation from './SideNavigation'

interface MainLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
  topContent?: React.ReactNode
  bottomContent?: React.ReactNode
}

/**
 * Main layout component with sidebar navigation and structured content areas
 * Fully responsive across all device sizes
 */
const MainLayout: React.FC<MainLayoutProps> = React.memo(({
  children,
  title,
  description,
  className,
  topContent,
  bottomContent
}) => {
  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar Navigation */}
      <SideNavigation />

      {/* Main Content */}
      <main className={cn(
        "flex-1 flex flex-col overflow-hidden",
        "min-w-0", // Prevent flex item from overflowing
        className
      )}>
        {/* Header - Mobile optimized */}
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
          <div className="flex h-12 sm:h-14 xl:h-16 items-center px-3 sm:px-4 lg:px-6 xl:px-8">
            {/* Title and description */}
            <div className="flex flex-col min-w-0 flex-1">
              {title && (
                <h1 className="text-sm sm:text-base lg:text-lg font-semibold text-foreground truncate">
                  {title}
                </h1>
              )}
              {description && (
                <p className="text-xs sm:text-sm text-muted-foreground truncate hidden sm:block">
                  {description}
                </p>
              )}
            </div>
          </div>
        </header>

        {/* Top Content (Filters) - Mobile optimized */}
        {topContent && (
          <div className="border-b border-border bg-background flex-shrink-0">
            <div className="px-3 sm:px-4 lg:px-6 xl:px-8 py-1.5 sm:py-2">
              {topContent}
            </div>
          </div>
        )}

        {/* Scrollable Content Area - Mobile optimized */}
        <div className="flex-1 overflow-auto">
          <div className="px-3 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 xl:py-6">
            {children}
          </div>
        </div>

        {/* Bottom Content (Pagination) - Mobile optimized */}
        {bottomContent && (
          <div className="border-t border-border bg-background flex-shrink-0">
            <div className="px-3 sm:px-4 lg:px-6 xl:px-8 py-2 sm:py-3">
              {bottomContent}
            </div>
          </div>
        )}
      </main>
    </div>
  )
})

MainLayout.displayName = 'MainLayout'

export default MainLayout
