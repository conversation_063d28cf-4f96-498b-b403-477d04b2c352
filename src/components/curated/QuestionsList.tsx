import React from 'react'
import { motion } from 'framer-motion'
import { Question } from '../../services/curatedService'

interface QuestionsListProps {
  questions: Question[]
  className?: string
}

/**
 * Reusable component for displaying a list of questions
 * Used in QuestionsModal and for viewing original questions
 */
export const QuestionsList: React.FC<QuestionsListProps> = ({
  questions,
  className = ""
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {questions.map((question, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700"
        >
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 flex items-center justify-center text-sm font-medium flex-shrink-0 mt-1">
              {index + 1}
            </div>
            <div className="flex-1">
              <p className="text-slate-900 dark:text-white font-medium mb-2">
                {question.question.text}
              </p>
              {question.question.translated_text && (
                <p className="text-slate-600 dark:text-slate-400 text-sm mb-3">
                  {question.question.translated_text}
                </p>
              )}
              {question.question.options && (
                <div className="space-y-1">
                  {Object.entries(question.question.options).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2 text-sm">
                      <span className="w-6 h-6 rounded border border-slate-300 dark:border-slate-600 flex items-center justify-center text-xs font-medium">
                        {key.toUpperCase()}
                      </span>
                      <span className="text-slate-700 dark:text-slate-300">{value}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}
