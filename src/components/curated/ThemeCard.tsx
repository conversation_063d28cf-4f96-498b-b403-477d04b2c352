import React from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, BookOpen, FileText } from 'lucide-react'
import { Theme } from '../../services/curatedService'
import { cn } from '../../utils/cn'

export interface ThemeCardProps {
  theme: Theme
  onClick?: (theme: Theme) => void
  className?: string
  showStats?: boolean
}

/**
 * ThemeCard Component
 * Displays a theme with icon, colors, and statistics
 * Used in Atelier page for theme discovery
 */
const ThemeCard: React.FC<ThemeCardProps> = React.memo(({
  theme,
  onClick,
  className,
  showStats = true
}) => {
  const handleClick = () => {
    onClick?.(theme)
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      culture: 'from-purple-500 to-pink-500',
      geography: 'from-green-500 to-teal-500',
      history: 'from-amber-500 to-orange-500',
      language: 'from-blue-500 to-indigo-500',
      literature: 'from-rose-500 to-red-500',
      science: 'from-cyan-500 to-blue-500',
      sports: 'from-emerald-500 to-green-500',
      politics: 'from-gray-500 to-slate-500',
      economy: 'from-yellow-500 to-amber-500',
      religion: 'from-violet-500 to-purple-500',
      art: 'from-pink-500 to-rose-500',
      music: 'from-indigo-500 to-blue-500',
      dance: 'from-fuchsia-500 to-pink-500',
      food: 'from-orange-500 to-red-500',
      festivals: 'from-yellow-500 to-orange-500',
      traditions: 'from-purple-500 to-violet-500',
      customs: 'from-teal-500 to-cyan-500',
      wildlife: 'from-green-500 to-emerald-500',
      nature: 'from-lime-500 to-green-500',
      tourism: 'from-sky-500 to-blue-500',
      education: 'from-blue-500 to-purple-500',
      technology: 'from-gray-500 to-blue-500',
      health: 'from-red-500 to-pink-500',
      agriculture: 'from-green-500 to-yellow-500',
      business: 'from-blue-500 to-gray-500',
      entertainment: 'from-purple-500 to-pink-500'
    }
    return colors[category] || 'from-gray-500 to-slate-500'
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.05, y: -8 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'relative bg-card border border-border rounded-xl p-6 cursor-pointer',
        'hover:shadow-xl hover:border-primary/30 transition-all duration-300',
        'group overflow-hidden',
        className
      )}
      onClick={handleClick}
    >
      {/* Background gradient */}
      <div 
        className={cn(
          'absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity duration-300',
          getCategoryColor(theme.category)
        )}
      />

      {/* Theme icon and color indicator */}
      <div className="relative z-10 flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div 
            className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl shadow-sm"
            style={{ backgroundColor: `${theme.color}20`, color: theme.color }}
          >
            {theme.icon}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
              {theme.name_en}
            </h3>
            <p className="text-sm text-muted-foreground capitalize">
              {theme.category}
            </p>
          </div>
        </div>
        <ArrowRight className="w-5 h-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
      </div>

      {/* Theme description */}
      <div className="relative z-10 mb-4">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {theme.description_en || theme.description}
        </p>
      </div>

      {/* Statistics */}
      {showStats && theme.statistics && (
        <div className="relative z-10 grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <BookOpen className="w-4 h-4 text-primary" />
              <span className="text-lg font-semibold text-foreground">
                {theme.statistics.total_content_sets}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">Content Sets</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <FileText className="w-4 h-4 text-primary" />
              <span className="text-lg font-semibold text-foreground">
                {theme.statistics.total_content_items}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">Total Items</p>
          </div>
        </div>
      )}

      {/* Active indicator */}
      {theme.is_active && (
        <div className="absolute top-3 right-3 w-2 h-2 bg-green-500 rounded-full" />
      )}

      {/* Hover effect overlay */}
      <div 
        className={cn(
          'absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-10 transition-opacity duration-300',
          getCategoryColor(theme.category)
        )}
      />
    </motion.div>
  )
})

ThemeCard.displayName = 'ThemeCard'

export default ThemeCard
