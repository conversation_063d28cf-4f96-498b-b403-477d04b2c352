import React from 'react'
import { motion } from 'framer-motion'
import { Search, BookOpen, Palette, Zap, RefreshCw } from 'lucide-react'
import { cn } from '../../utils/cn'

export interface EmptyStateProps {
  type: 'search' | 'content' | 'themes' | 'prompts' | 'error'
  title?: string
  description?: string
  actionLabel?: string
  onAction?: () => void
  className?: string
}

/**
 * EmptyState Component
 * Displays appropriate empty states for different scenarios
 * Used across all curated content pages
 */
const EmptyState: React.FC<EmptyStateProps> = React.memo(({
  type,
  title,
  description,
  actionLabel,
  onAction,
  className
}) => {
  const getEmptyStateConfig = () => {
    switch (type) {
      case 'search':
        return {
          icon: Search,
          title: title || 'No results found',
          description: description || 'Try adjusting your search terms or filters to find what you\'re looking for.',
          actionLabel: actionLabel || 'Clear filters',
          iconColor: 'text-blue-500',
          bgColor: 'bg-blue-50 dark:bg-blue-950/20'
        }
      case 'content':
        return {
          icon: BookOpen,
          title: title || 'No content available',
          description: description || 'There are no content sets available at the moment. Check back later or try different filters.',
          actionLabel: actionLabel || 'Refresh',
          iconColor: 'text-amber-500',
          bgColor: 'bg-amber-50 dark:bg-amber-950/20'
        }
      case 'themes':
        return {
          icon: Palette,
          title: title || 'No themes found',
          description: description || 'No themes match your current criteria. Try adjusting your search or category filters.',
          actionLabel: actionLabel || 'View all themes',
          iconColor: 'text-purple-500',
          bgColor: 'bg-purple-50 dark:bg-purple-950/20'
        }
      case 'prompts':
        return {
          icon: Zap,
          title: title || 'No prompts yet',
          description: description || 'You haven\'t generated any content yet. Start by creating your first prompt.',
          actionLabel: actionLabel || 'Create prompt',
          iconColor: 'text-green-500',
          bgColor: 'bg-green-50 dark:bg-green-950/20'
        }
      case 'error':
        return {
          icon: RefreshCw,
          title: title || 'Something went wrong',
          description: description || 'We encountered an error while loading the content. Please try again.',
          actionLabel: actionLabel || 'Try again',
          iconColor: 'text-red-500',
          bgColor: 'bg-red-50 dark:bg-red-950/20'
        }
      default:
        return {
          icon: Search,
          title: title || 'No data available',
          description: description || 'There\'s nothing to show here right now.',
          actionLabel: actionLabel || 'Refresh',
          iconColor: 'text-gray-500',
          bgColor: 'bg-gray-50 dark:bg-gray-950/20'
        }
    }
  }

  const config = getEmptyStateConfig()
  const Icon = config.icon

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'flex flex-col items-center justify-center py-12 px-6 text-center',
        className
      )}
    >
      {/* Icon */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className={cn(
          'w-16 h-16 rounded-full flex items-center justify-center mb-6',
          config.bgColor
        )}
      >
        <Icon className={cn('w-8 h-8', config.iconColor)} />
      </motion.div>

      {/* Title */}
      <motion.h3
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="text-lg font-semibold text-foreground mb-2"
      >
        {config.title}
      </motion.h3>

      {/* Description */}
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.3 }}
        className="text-muted-foreground max-w-md mb-6"
      >
        {config.description}
      </motion.p>

      {/* Action button */}
      {onAction && (
        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          onClick={onAction}
          className={cn(
            'px-4 py-2 rounded-lg font-medium transition-all duration-200',
            'bg-primary text-primary-foreground hover:bg-primary/90',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'transform hover:scale-105 active:scale-95'
          )}
        >
          {config.actionLabel}
        </motion.button>
      )}
    </motion.div>
  )
})

EmptyState.displayName = 'EmptyState'

export default EmptyState
