import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../utils/cn'

export interface LoadingGridProps {
  count?: number
  className?: string
  cardClassName?: string
  type?: 'content' | 'theme'
}

/**
 * LoadingGrid Component
 * Displays skeleton loading cards while data is being fetched
 * Used across all curated content pages
 */
const LoadingGrid: React.FC<LoadingGridProps> = React.memo(({
  count = 8,
  className,
  cardClassName,
  type = 'content'
}) => {
  const getGridCols = () => {
    if (type === 'theme') {
      return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
    }
    return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }

  const renderContentSkeleton = () => (
    <div className="space-y-4">
      {/* Theme indicator */}
      <div className="h-1 bg-muted rounded-full w-full" />
      
      {/* Theme info */}
      <div className="flex items-center gap-2">
        <div className="w-6 h-6 bg-muted rounded" />
        <div className="h-4 bg-muted rounded w-20" />
      </div>

      {/* Title and description */}
      <div className="space-y-2">
        <div className="h-5 bg-muted rounded w-3/4" />
        <div className="h-4 bg-muted rounded w-full" />
        <div className="h-4 bg-muted rounded w-2/3" />
      </div>

      {/* Metadata */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-muted rounded-full" />
            <div className="w-3 h-3 bg-muted rounded-full" />
            <div className="w-3 h-3 bg-muted rounded-full" />
            <div className="h-3 bg-muted rounded w-12 ml-1" />
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-muted rounded" />
            <div className="h-3 bg-muted rounded w-12" />
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="h-6 bg-muted rounded-full w-20" />
          <div className="h-4 bg-muted rounded w-16" />
        </div>
        
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-muted rounded" />
          <div className="h-3 bg-muted rounded w-20" />
        </div>
      </div>
    </div>
  )

  const renderThemeSkeleton = () => (
    <div className="space-y-4">
      {/* Icon and header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-muted rounded-lg" />
          <div className="space-y-1">
            <div className="h-5 bg-muted rounded w-24" />
            <div className="h-4 bg-muted rounded w-16" />
          </div>
        </div>
        <div className="w-5 h-5 bg-muted rounded" />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <div className="h-4 bg-muted rounded w-full" />
        <div className="h-4 bg-muted rounded w-3/4" />
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center space-y-1">
          <div className="flex items-center justify-center gap-1">
            <div className="w-4 h-4 bg-muted rounded" />
            <div className="h-6 bg-muted rounded w-8" />
          </div>
          <div className="h-3 bg-muted rounded w-16 mx-auto" />
        </div>
        <div className="text-center space-y-1">
          <div className="flex items-center justify-center gap-1">
            <div className="w-4 h-4 bg-muted rounded" />
            <div className="h-6 bg-muted rounded w-8" />
          </div>
          <div className="h-3 bg-muted rounded w-16 mx-auto" />
        </div>
      </div>
    </div>
  )

  return (
    <div className={cn(
      'grid gap-6',
      getGridCols(),
      className
    )}>
      {Array.from({ length: count }, (_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
          className={cn(
            'bg-card border border-border rounded-lg p-4 sm:p-6',
            'animate-pulse',
            cardClassName
          )}
        >
          {type === 'content' ? renderContentSkeleton() : renderThemeSkeleton()}
        </motion.div>
      ))}
    </div>
  )
})

LoadingGrid.displayName = 'LoadingGrid'

export default LoadingGrid
