import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Filter, ChevronDown, X } from 'lucide-react'
import { cn } from '../../utils/cn'

export interface FilterOption {
  value: string
  label: string
  count?: number
}

export interface FilterGroup {
  key: string
  label: string
  options: FilterOption[]
  type: 'select' | 'multiselect' | 'range'
  value?: string | string[] | number[]
}

export interface FilterPanelProps {
  filters: FilterGroup[]
  onFilterChange: (key: string, value: string | string[] | number[]) => void
  onClearAll?: () => void
  className?: string
  isCollapsible?: boolean
  defaultExpanded?: boolean
}

/**
 * FilterPanel Component
 * Reusable filtering interface with multiple filter types
 * Used in Anthology page for content filtering
 */
const FilterPanel: React.FC<FilterPanelProps> = React.memo(({
  filters,
  onFilterChange,
  onClearAll,
  className,
  isCollapsible = true,
  defaultExpanded = true
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(filters.map(f => f.key))
  )

  const toggleGroup = (key: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(key)) {
      newExpanded.delete(key)
    } else {
      newExpanded.add(key)
    }
    setExpandedGroups(newExpanded)
  }

  const hasActiveFilters = filters.some(filter => {
    if (Array.isArray(filter.value)) {
      return filter.value.length > 0
    }
    return filter.value !== undefined && filter.value !== ''
  })

  const getActiveFilterCount = () => {
    return filters.reduce((count, filter) => {
      if (Array.isArray(filter.value)) {
        return count + filter.value.length
      }
      return filter.value !== undefined && filter.value !== '' ? count + 1 : count
    }, 0)
  }

  const renderSelectFilter = (filter: FilterGroup) => (
    <div className="relative">
      <select
        value={filter.value as string || ''}
        onChange={(e) => onFilterChange(filter.key, e.target.value)}
        className={cn(
          'w-full px-4 py-3 rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
          'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
          'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
          'hover:border-blue-300/70 dark:hover:border-blue-700/50',
          'transition-all duration-300 ease-out',
          'appearance-none cursor-pointer',
          'shadow-sm hover:shadow-md'
        )}
      >
        <option value="">✨ All {filter.label}</option>
        {filter.options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label} {option.count && `(${option.count})`}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-400 pointer-events-none" />
    </div>
  )

  const renderMultiSelectFilter = (filter: FilterGroup) => {
    const selectedValues = (filter.value as string[]) || []

    return (
      <div className="space-y-2">
        {filter.options.map((option) => (
          <motion.label
            key={option.value}
            className="flex items-center gap-2.5 cursor-pointer group"
            whileHover={{ x: 1 }}
            transition={{ duration: 0.2 }}
          >
            <div className="relative">
              <input
                type="checkbox"
                checked={selectedValues.includes(option.value)}
                onChange={(e) => {
                  const newValues = e.target.checked
                    ? [...selectedValues, option.value]
                    : selectedValues.filter(v => v !== option.value)
                  onFilterChange(filter.key, newValues)
                }}
                className={cn(
                  'w-3.5 h-3.5 sm:w-4 sm:h-4 rounded border-2 border-blue-300 dark:border-blue-700',
                  'text-blue-600 focus:ring-2 focus:ring-blue-500/30',
                  'transition-all duration-200',
                  'checked:bg-blue-600 checked:border-blue-600'
                )}
              />
            </div>
            <span className={cn(
              'text-xs sm:text-sm transition-colors duration-200',
              selectedValues.includes(option.value)
                ? 'text-blue-700 dark:text-blue-300 font-medium'
                : 'text-slate-600 dark:text-slate-400 group-hover:text-slate-800 dark:group-hover:text-slate-200'
            )}>
              {option.label} {option.count && (
                <span className="text-xs text-slate-500 dark:text-slate-500">
                  ({option.count})
                </span>
              )}
            </span>
          </motion.label>
        ))}
      </div>
    )
  }

  const renderRangeFilter = (filter: FilterGroup) => {
    const values = (filter.value as number[]) || [1, 3]
    
    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <input
            type="range"
            min="1"
            max="3"
            value={values[0]}
            onChange={(e) => onFilterChange(filter.key, [parseInt(e.target.value), values[1]])}
            className="flex-1"
          />
          <span className="text-sm text-muted-foreground w-8">
            {values[0]}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="range"
            min="1"
            max="3"
            value={values[1]}
            onChange={(e) => onFilterChange(filter.key, [values[0], parseInt(e.target.value)])}
            className="flex-1"
          />
          <span className="text-sm text-muted-foreground w-8">
            {values[1]}
          </span>
        </div>
        <div className="text-xs text-muted-foreground">
          Difficulty: {values[0]} - {values[1]}
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      'bg-gradient-to-br from-white/90 to-blue-50/50 dark:from-gray-900/90 dark:to-blue-950/30',
      'border-2 border-blue-200/50 dark:border-blue-800/30 rounded-2xl overflow-hidden',
      'shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm',
      className
    )}>
      {/* Header - Responsive padding for all screen sizes */}
      <div className="flex items-center justify-between p-3 sm:p-4 xl:p-5 border-b border-blue-200/30 dark:border-blue-800/30 bg-white/50 dark:bg-gray-900/50">
        <div className="flex items-center gap-2.5">
          <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg">
            <Filter className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </div>
          <div>
            <h3 className="text-sm sm:text-base font-semibold text-slate-800 dark:text-white">Filters</h3>
            {hasActiveFilters && (
              <span className="px-2 py-0.5 text-xs bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full font-medium shadow-sm mt-0.5 inline-block">
                {getActiveFilterCount()} active
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-1.5">
          {hasActiveFilters && onClearAll && (
            <motion.button
              onClick={onClearAll}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-1.5 px-2 py-1.5 text-xs text-slate-600 dark:text-slate-400 hover:text-red-600 dark:hover:text-red-400 transition-colors rounded-lg hover:bg-red-50 dark:hover:bg-red-950/20 touch-target"
            >
              <X className="w-3 h-3" />
              <span className="hidden sm:inline">Clear All</span>
            </motion.button>
          )}
          {isCollapsible && (
            <motion.button
              onClick={() => setIsExpanded(!isExpanded)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-1.5 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors touch-target"
            >
              <ChevronDown className={cn(
                'w-3.5 h-3.5 sm:w-4 sm:h-4 transition-transform duration-300 text-blue-600 dark:text-blue-400',
                isExpanded ? 'rotate-180' : ''
              )} />
            </motion.button>
          )}
        </div>
      </div>

      {/* Filter Groups */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-3 sm:p-4 xl:p-5 space-y-4">
              {filters.map((filter) => (
                <div key={filter.key} className="space-y-2">
                  <motion.button
                    onClick={() => toggleGroup(filter.key)}
                    className="flex items-center justify-between w-full text-left p-2 sm:p-2.5 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-950/20 transition-all duration-200 group"
                    whileHover={{ x: 2 }}
                  >
                    <span className="text-sm font-semibold text-slate-700 dark:text-slate-300 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                      {filter.label}
                    </span>
                    <ChevronDown className={cn(
                      'w-3.5 h-3.5 sm:w-4 sm:h-4 transition-all duration-300 text-blue-500',
                      expandedGroups.has(filter.key) ? 'rotate-180' : '',
                      'group-hover:text-blue-600'
                    )} />
                  </motion.button>

                  <AnimatePresence>
                    {expandedGroups.has(filter.key) && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="pt-2 px-2 pb-1.5 bg-white/30 dark:bg-gray-800/30 rounded-lg border border-blue-100 dark:border-blue-900/30">
                          {filter.type === 'select' && renderSelectFilter(filter)}
                          {filter.type === 'multiselect' && renderMultiSelectFilter(filter)}
                          {filter.type === 'range' && renderRangeFilter(filter)}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
})

FilterPanel.displayName = 'FilterPanel'

export default FilterPanel
