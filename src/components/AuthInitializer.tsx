import { useEffect } from 'react'
import { useAppDispatch } from '../store/hooks'
import { initializeAuth, getCurrentUser } from '../store/slices/authSlice'
import { authService } from '../services/auth/authService'

/**
 * AuthInitializer - Component to initialize auth state on app load
 */
const AuthInitializer = () => {
  const dispatch = useAppDispatch()

  useEffect(() => {
    const initializeAuthState = async () => {
      // Initialize auth state with stored token
      dispatch(initializeAuth())
      
      // If there's a stored token, verify it
      const token = authService.getStoredToken()
      if (token) {
        try {
          await dispatch(getCurrentUser()).unwrap()
        } catch (error) {
          // Token is invalid, logout will be handled by the thunk
          console.warn('Failed to verify token, token may be invalid')
        }
      }
    }

    initializeAuthState()
  }, [dispatch])

  return null // This component doesn't render anything
}

export default AuthInitializer
