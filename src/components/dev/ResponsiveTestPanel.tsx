import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Monitor, Smartphone, Tablet, Eye, CheckCircle, XCircle } from 'lucide-react'
import { responsiveTester, VIEWPORT_SIZES, type ResponsiveTestResult } from '../../utils/responsive-test'

/**
 * Development component for testing responsive design
 * Only visible in development mode
 */
export const ResponsiveTestPanel: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [currentViewport, setCurrentViewport] = useState(VIEWPORT_SIZES[0])
  const [testResults, setTestResults] = useState<ResponsiveTestResult[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const runTests = async () => {
    setIsRunningTests(true)
    try {
      const results = responsiveTester.testAllViewports()
      setTestResults(results)
    } catch (error) {
      console.error('Error running responsive tests:', error)
    } finally {
      setIsRunningTests(false)
    }
  }

  const changeViewport = (viewport: typeof VIEWPORT_SIZES[0]) => {
    setCurrentViewport(viewport)
    
    // Apply viewport size to iframe or window for testing
    if (typeof window !== 'undefined') {
      // For development, we can log the viewport change
      console.log(`Testing viewport: ${viewport.name} (${viewport.width}x${viewport.height})`)
    }
  }

  const getIcon = (category: string) => {
    switch (category) {
      case 'mobile': return <Smartphone className="w-4 h-4" />
      case 'tablet': return <Tablet className="w-4 h-4" />
      case 'desktop': return <Monitor className="w-4 h-4" />
      default: return <Eye className="w-4 h-4" />
    }
  }

  const getStatusIcon = (passed: boolean) => {
    return passed ? 
      <CheckCircle className="w-4 h-4 text-green-500" /> : 
      <XCircle className="w-4 h-4 text-red-500" />
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Eye className="w-5 h-5" />
      </motion.button>

      {/* Test Panel */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 20, scale: 0.9 }}
          className="absolute bottom-16 right-0 w-80 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-4"
        >
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Responsive Test Panel
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ×
              </button>
            </div>

            {/* Current Viewport */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Current Viewport
              </label>
              <div className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                {getIcon(currentViewport.category)}
                <span className="text-sm">
                  {currentViewport.name} ({currentViewport.width}×{currentViewport.height})
                </span>
              </div>
            </div>

            {/* Viewport Selector */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Test Viewport
              </label>
              <select
                value={`${currentViewport.width}x${currentViewport.height}`}
                onChange={(e) => {
                  const [width, height] = e.target.value.split('x').map(Number)
                  const viewport = VIEWPORT_SIZES.find(v => v.width === width && v.height === height)
                  if (viewport) changeViewport(viewport)
                }}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-sm"
              >
                {VIEWPORT_SIZES.map((viewport) => (
                  <option key={`${viewport.width}x${viewport.height}`} value={`${viewport.width}x${viewport.height}`}>
                    {viewport.name} ({viewport.width}×{viewport.height})
                  </option>
                ))}
              </select>
            </div>

            {/* Test Actions */}
            <div className="space-y-2">
              <button
                onClick={runTests}
                disabled={isRunningTests}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                {isRunningTests ? 'Running Tests...' : 'Run Responsive Tests'}
              </button>
            </div>

            {/* Test Results */}
            {testResults.length > 0 && (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Test Results
                </h4>
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs"
                    >
                      <div className="flex items-center gap-2">
                        {getIcon(result.viewport.category)}
                        <span>{result.viewport.name}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(result.passed)}
                        <span className={result.passed ? 'text-green-600' : 'text-red-600'}>
                          {result.issues.length} issues
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="text-xs text-gray-500 dark:text-gray-400 border-t pt-2">
              <div>Current window: {typeof window !== 'undefined' ? `${window.innerWidth}×${window.innerHeight}` : 'N/A'}</div>
              <div>Breakpoint: {
                typeof window !== 'undefined' ? 
                  window.innerWidth < 640 ? 'xs/sm' :
                  window.innerWidth < 768 ? 'sm' :
                  window.innerWidth < 1024 ? 'md' :
                  window.innerWidth < 1280 ? 'lg' :
                  window.innerWidth < 1536 ? 'xl' : '2xl'
                : 'N/A'
              }</div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default ResponsiveTestPanel
