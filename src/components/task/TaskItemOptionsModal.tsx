import React from 'react'
import { motion } from 'framer-motion'
import * as Dialog from '@radix-ui/react-dialog'
import { 
  RotateCcw, 
  Eye, 
  BookOpen, 
  X,
  Clock,
  CheckCircle
} from 'lucide-react'
import { cn } from '../../utils/cn'

interface TaskItemOptionsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  taskId: string
  taskIndex: number
  taskSetId: string
  onRetryTask: () => void
  onViewQuiz: () => void
  onViewStory: () => void
  hasStory?: boolean
}

/**
 * Modal for displaying task item options: Retry Again, View Quiz, View Story
 */
const TaskItemOptionsModal: React.FC<TaskItemOptionsModalProps> = ({
  open,
  onOpenChange,
  taskId,
  taskIndex,
  taskSetId,
  onRetryTask,
  onViewQuiz,
  onViewStory,
  hasStory = true
}) => {
  const handleRetryClick = () => {
    onRetryTask()
    onOpenChange(false)
  }

  const handleViewQuizClick = () => {
    onViewQuiz()
    onOpenChange(false)
  }

  const handleViewStoryClick = () => {
    onViewStory()
    onOpenChange(false)
  }

  const options = [
    {
      id: 'retry',
      title: 'Retry Again',
      description: 'Start this task over from the beginning',
      icon: RotateCcw,
      iconBg: 'bg-gradient-to-br from-orange-500 to-red-600',
      onClick: handleRetryClick,
      available: true
    },
    {
      id: 'quiz',
      title: 'View Quiz',
      description: 'Go directly to the quiz questions',
      icon: Eye,
      iconBg: 'bg-gradient-to-br from-blue-500 to-indigo-600',
      onClick: handleViewQuizClick,
      available: true
    },
    {
      id: 'story',
      title: 'View Story',
      description: 'Read the story content for this task',
      icon: BookOpen,
      iconBg: 'bg-gradient-to-br from-green-500 to-emerald-600',
      onClick: handleViewStoryClick,
      available: hasStory
    }
  ]

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 z-50" />
        <Dialog.Content className="fixed left-[50%] top-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%] duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                  {taskIndex + 1}
                </div>
                <div>
                  <Dialog.Title className="text-xl font-semibold text-slate-900 dark:text-white">
                    Task {taskIndex + 1}
                  </Dialog.Title>
                  <Dialog.Description className="text-sm text-slate-600 dark:text-slate-400">
                    Choose an action for this task
                  </Dialog.Description>
                </div>
              </div>
              <Dialog.Close asChild>
                <button className="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                  <X className="h-5 w-5 text-slate-500" />
                </button>
              </Dialog.Close>
            </div>

            {/* Options */}
            <div className="p-6 space-y-3">
              {options.map((option, index) => (
                <motion.button
                  key={option.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={option.onClick}
                  disabled={!option.available}
                  className={cn(
                    "w-full flex items-center gap-4 p-4 rounded-xl border transition-all duration-200",
                    option.available
                      ? "border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 cursor-pointer"
                      : "border-slate-100 dark:border-slate-800 bg-slate-50 dark:bg-slate-800/50 cursor-not-allowed opacity-50"
                  )}
                >
                  <div className={cn(
                    "w-12 h-12 rounded-xl flex items-center justify-center shadow-sm",
                    option.available ? option.iconBg : "bg-slate-300 dark:bg-slate-700"
                  )}>
                    <option.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <h3 className={cn(
                      "font-medium",
                      option.available 
                        ? "text-slate-900 dark:text-white" 
                        : "text-slate-500 dark:text-slate-400"
                    )}>
                      {option.title}
                    </h3>
                    <p className={cn(
                      "text-sm",
                      option.available 
                        ? "text-slate-600 dark:text-slate-400" 
                        : "text-slate-400 dark:text-slate-500"
                    )}>
                      {option.available ? option.description : 'Not available for this task'}
                    </p>
                  </div>
                  {option.available && (
                    <div className="text-slate-400">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  )}
                </motion.button>
              ))}
            </div>

            {/* Footer */}
            <div className="px-6 py-4 bg-slate-50 dark:bg-slate-800/50 border-t border-slate-200 dark:border-slate-700">
              <p className="text-xs text-slate-500 dark:text-slate-400 text-center">
                Task ID: {taskId}
              </p>
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

export default TaskItemOptionsModal
