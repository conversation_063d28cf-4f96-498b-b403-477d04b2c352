import React from 'react';
import { Tooltip, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { formatTaskType } from '../../utils/taskUtils';

interface TaskTypeBadgeProps {
  type: string;
  className?: string;
}

/**
 * TaskTypeBadge - Displays a task type with a tooltip showing a more descriptive name
 */
const TaskTypeBadge: React.FC<TaskTypeBadgeProps> = ({ type, className = '' }) => {
  const { display, tooltip } = formatTaskType(type);
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span 
            className={`inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full bg-muted text-muted-foreground ${className}`}
          >
            {display}
          </span>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <p className="text-sm">{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default TaskTypeBadge;
