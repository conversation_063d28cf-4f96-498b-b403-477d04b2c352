import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../store/hooks'
import { markStoryAsVisited, dismissStoryNotification, undismissStoryNotification } from '../../store/slices/storySlice'
import { Eye, Sparkles, X, Bell, Check } from 'lucide-react'
import { cn } from '../../utils/cn'

interface PersistentStoryToggleProps {
  className?: string
  variant?: 'default' | 'compact' | 'floating' | 'notification'
  position?: 'top-right' | 'bottom-right' | 'bottom-left' | 'top-left'
  showCloseButton?: boolean
  onClose?: () => void
  showVisitedStories?: boolean // New prop to show visited stories with tick icon
}

/**
 * Persistent Story Toggle Component
 * Shows "Visit Story" button for unvisited stories
 * Automatically hides when all stories are visited
 */
const PersistentStoryToggle: React.FC<PersistentStoryToggleProps> = ({
  className,
  variant = 'default',
  position = 'top-right',
  showCloseButton = false,
  onClose,
  showVisitedStories = false
}) => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { stories, dismissedNotifications } = useAppSelector((state) => state.story as any)

  // Get unvisited and non-dismissed stories
  const unvisitedStories = Object.values(stories || {}).filter((story: any) =>
    !story.visited && !dismissedNotifications.includes(story.storyId)
  )

  // Get visited stories if showVisitedStories is true
  const visitedStories = Object.values(stories || {}).filter((story: any) => story.visited)

  // Determine which stories to show
  const storiesToShow = showVisitedStories ? visitedStories : unvisitedStories
  const hasStoriesToShow = storiesToShow.length > 0

  // Get the most recent story to show
  const mostRecentStory = storiesToShow.sort((a: any, b: any) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )[0] as any

  const handleVisitStory = () => {
    if (mostRecentStory) {
      // Mark as visited if not already visited
      if (!mostRecentStory.visited) {
        dispatch(markStoryAsVisited(mostRecentStory.storyId))
        // Remove from dismissed list since it's now visited
        dispatch(undismissStoryNotification(mostRecentStory.storyId))
      }

      // Call onClose if provided (for notification variant)
      if (onClose) {
        onClose()
      }

      // Navigate to story
      navigate(`/story/${mostRecentStory.storyId}`)
    }
  }

  const handleDismissNotification = () => {
    if (mostRecentStory && onClose) {
      // Add to dismissed list (temporary hide)
      dispatch(dismissStoryNotification(mostRecentStory.storyId))
      onClose()
    }
  }

  // Position classes
  const positionClasses = {
    'top-right': 'top-4 right-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-left': 'top-4 left-4'
  }

  // Variant styles
  const variantStyles = {
    default: {
      container: "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg hover:shadow-xl",
      button: "px-4 py-3 rounded-xl font-medium",
      icon: "h-5 w-5",
      text: "text-sm"
    },
    compact: {
      container: "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-md hover:shadow-lg",
      button: "px-3 py-2 rounded-lg font-medium",
      icon: "h-4 w-4",
      text: "text-xs"
    },
    floating: {
      container: "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-2xl hover:shadow-3xl",
      button: "p-4 rounded-full",
      icon: "h-6 w-6",
      text: "sr-only"
    },
    notification: {
      container: "bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-700 shadow-xl hover:shadow-2xl",
      button: "px-4 py-3 rounded-lg font-medium text-purple-700 dark:text-purple-300",
      icon: "h-5 w-5",
      text: "text-sm"
    }
  }

  const styles = variantStyles[variant]

  if (!hasStoriesToShow) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: 20 }}
        className={cn(
          (variant === 'floating' || variant === 'notification') ? 'fixed z-50' : 'relative',
          (variant === 'floating' || variant === 'notification') && positionClasses[position],
          className
        )}
      >
        {variant === 'notification' ? (
          <div className={cn(
            "flex items-center gap-3 p-4 rounded-lg transition-all duration-200",
            styles.container
          )}>
            {/* Notification Icon */}
            <motion.div
              animate={showVisitedStories ? {} : {
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              {showVisitedStories ? (
                <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
              ) : (
                <Bell className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              )}
            </motion.div>

            {/* Content */}
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                {showVisitedStories ? 'Story Visited!' : 'New Story Ready!'}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {showVisitedStories
                  ? 'You have viewed this story'
                  : 'Your personalized story has been generated'
                }
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <motion.button
                onClick={handleVisitStory}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={cn(
                  "px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                  showVisitedStories
                    ? "bg-green-600 text-white hover:bg-green-700"
                    : "bg-purple-600 text-white hover:bg-purple-700"
                )}
              >
                {showVisitedStories ? 'View Again' : 'Visit'}
              </motion.button>

              {showCloseButton && onClose && (
                <motion.button
                  onClick={handleDismissNotification}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                  <X className="h-4 w-4" />
                </motion.button>
              )}
            </div>
          </div>
        ) : (
          <motion.button
            onClick={handleVisitStory}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={cn(
              "flex items-center gap-2 transition-all duration-200",
              styles.container,
              styles.button
            )}
          >
            {/* Sparkle animation */}
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.2, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Sparkles className={styles.icon} />
            </motion.div>

            {variant !== 'floating' && (
              <>
                <span className={styles.text}>
                  {showVisitedStories
                    ? `Visited Stories (${visitedStories.length})`
                    : `Visit Story (${unvisitedStories.length})`
                  }
                </span>
                {showVisitedStories ? (
                  <Check className={styles.icon} />
                ) : (
                  <Eye className={styles.icon} />
                )}
              </>
            )}

            {variant === 'floating' && (
              <span className={styles.text}>
                {showVisitedStories
                  ? `Visited Stories (${visitedStories.length})`
                  : `Visit Story (${unvisitedStories.length})`
                }
              </span>
            )}
          </motion.button>
        )}

        {/* Pulse indicator for floating variant */}
        {variant === 'floating' && (
          <motion.div
            className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 opacity-30"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.3, 0, 0.3]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}

        {/* Story count badge for default variant */}
        {variant === 'default' && storiesToShow.length > 1 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className={cn(
              "absolute -top-2 -right-2 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold",
              showVisitedStories ? "bg-green-500" : "bg-red-500"
            )}
          >
            {storiesToShow.length}
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  )
}

export default PersistentStoryToggle
