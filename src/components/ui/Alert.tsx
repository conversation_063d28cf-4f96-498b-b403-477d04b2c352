import React from 'react'
import * as AlertDialog from '@radix-ui/react-alert-dialog'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { cn } from '../../utils/cn'

interface AlertProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  correctAnswers?: string | string[]
  feedback?: string
  children?: React.ReactNode
  showAutoNavigate?: boolean
  autoNavigateSeconds?: number
  onNavigate?: () => void
  onStay?: () => void
}

/**
 * Alert component using Radix UI Alert Dialog
 * Used for showing submission results and feedback
 */
const Alert: React.FC<AlertProps> = ({
  open,
  onOpenChange,
  title,
  description,
  type = 'info',
  correctAnswers,
  feedback,
  children,
  showAutoNavigate = false,
  autoNavigateSeconds = 2,
  onNavigate,
  onStay
}) => {
  const [countdown, setCountdown] = React.useState(autoNavigateSeconds)

  // Countdown timer for auto-navigation
  React.useEffect(() => {
    if (open && showAutoNavigate) {
      setCountdown(autoNavigateSeconds)
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            // Auto-navigate when countdown reaches 0
            if (onNavigate) {
              onNavigate()
            }
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [open, showAutoNavigate, autoNavigateSeconds, onNavigate])
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-600" />
      case 'error':
        return <XCircle className="h-6 w-6 text-red-600" />
      case 'warning':
        return <AlertCircle className="h-6 w-6 text-yellow-600" />
      default:
        return <AlertCircle className="h-6 w-6 text-blue-600" />
    }
  }

  const getColorClasses = () => {
    switch (type) {
      case 'success':
        return {
          border: 'border-green-200 dark:border-green-800',
          bg: 'bg-green-50 dark:bg-green-900/20',
          title: 'text-green-800 dark:text-green-200',
          description: 'text-green-600 dark:text-green-300'
        }
      case 'error':
        return {
          border: 'border-red-200 dark:border-red-800',
          bg: 'bg-red-50 dark:bg-red-900/20',
          title: 'text-red-800 dark:text-red-200',
          description: 'text-red-600 dark:text-red-300'
        }
      case 'warning':
        return {
          border: 'border-yellow-200 dark:border-yellow-800',
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          title: 'text-yellow-800 dark:text-yellow-200',
          description: 'text-yellow-600 dark:text-yellow-300'
        }
      default:
        return {
          border: 'border-blue-200 dark:border-blue-800',
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          title: 'text-blue-800 dark:text-blue-200',
          description: 'text-blue-600 dark:text-blue-300'
        }
    }
  }

  const colors = getColorClasses()

  const formatCorrectAnswers = (answers: string | string[]) => {
    if (Array.isArray(answers)) {
      if (answers.length === 1) {
        return answers[0]
      }
      return answers.join(', ')
    }
    return answers
  }

  return (
    <AlertDialog.Root open={open} onOpenChange={onOpenChange}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay className="fixed inset-0 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 z-50" />
        <AlertDialog.Content className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg">
          <div className={cn(
            "flex items-start gap-4 p-4 rounded-lg border",
            colors.border,
            colors.bg
          )}>
            <div className="flex-shrink-0">
              {getIcon()}
            </div>
            <div className="flex-1 space-y-2">
              <AlertDialog.Title className={cn("text-lg font-semibold", colors.title)}>
                {title}
              </AlertDialog.Title>
              
              {description && (
                <AlertDialog.Description className={cn("text-sm", colors.description)}>
                  {description}
                </AlertDialog.Description>
              )}

              {/* Show correct answers if provided and type is error */}
              {correctAnswers && type === 'error' && (
                <div className="mt-3 p-3 bg-background/50 rounded-md border border-border/50">
                  <p className="text-sm font-medium text-foreground mb-1">
                    Correct answer{Array.isArray(correctAnswers) && correctAnswers.length > 1 ? 's' : ''}:
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {formatCorrectAnswers(correctAnswers)}
                  </p>
                </div>
              )}

              {/* Show feedback if provided */}
              {feedback && (
                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Feedback:
                  </p>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    {feedback}
                  </p>
                </div>
              )}

              {children}

              {/* Auto-navigate countdown */}
              {showAutoNavigate && countdown > 0 && (
                <div className={cn(
                  "mt-3 p-4 rounded-md border text-center",
                  type === 'success'
                    ? "bg-green-100 dark:bg-green-900/30 border-green-200 dark:border-green-800"
                    : "bg-blue-100 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800"
                )}>
                  <div className={cn(
                    "text-3xl font-bold mb-2",
                    type === 'success'
                      ? "text-green-700 dark:text-green-300"
                      : "text-blue-700 dark:text-blue-300"
                  )}>
                    {countdown}
                  </div>
                  <p className={cn(
                    "text-sm",
                    type === 'success'
                      ? "text-green-600 dark:text-green-400"
                      : "text-blue-600 dark:text-blue-400"
                  )}>
                    Moving to next task...
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            {showAutoNavigate ? (
              <>
                <AlertDialog.Action asChild>
                  <button
                    onClick={onStay}
                    className="inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-semibold text-foreground transition-colors hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  >
                    Stay
                  </button>
                </AlertDialog.Action>
                <AlertDialog.Action asChild>
                  <button
                    onClick={onNavigate}
                    className="inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  >
                    Navigate
                  </button>
                </AlertDialog.Action>
              </>
            ) : (
              <AlertDialog.Action asChild>
                <button className="inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                  OK
                </button>
              </AlertDialog.Action>
            )}
          </div>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  )
}

export default Alert
