import React, { useEffect, useRef } from 'react'
import * as anime from 'animejs'
import { cn } from '../../utils/cn'

interface SVGAnimatedCharacterProps {
  size?: number
  mood?: 'happy' | 'excited' | 'thinking' | 'celebrating' | 'focused'
  className?: string
}

/**
 * Advanced SVG Animated Character Component
 * Creates beautiful anime-style character with complex animations
 */
const SVGAnimatedCharacter: React.FC<SVGAnimatedCharacterProps> = ({
  size = 120,
  mood = 'happy',
  className
}) => {
  const svgRef = useRef<SVGSVGElement>(null)
  const headRef = useRef<SVGCircleElement>(null)
  const eyesRef = useRef<SVGGElement>(null)
  const hairRef = useRef<SVGGElement>(null)
  const auraRef = useRef<SVGGElement>(null)
  const sparklesRef = useRef<SVGGElement>(null)

  useEffect(() => {
    if (!anime.default) return

    // Head floating animation
    if (headRef.current) {
      anime.default({
        targets: headRef.current,
        translateY: [-3, 3],
        duration: 2000,
        loop: true,
        direction: 'alternate',
        easing: 'easeInOutSine'
      })
    }

    // Hair swaying animation
    if (hairRef.current) {
      anime.default({
        targets: hairRef.current.children,
        rotate: [-2, 2],
        duration: 3000,
        delay: anime.default.stagger(100),
        loop: true,
        direction: 'alternate',
        easing: 'easeInOutSine'
      })
    }

    // Eyes blinking
    if (eyesRef.current) {
      anime.default({
        targets: eyesRef.current.children,
        scaleY: [1, 0.1, 1],
        duration: 150,
        delay: 3000,
        loop: true,
        easing: 'easeInOutQuad'
      })
    }

    // Aura pulsing
    if (auraRef.current) {
      anime.default({
        targets: auraRef.current,
        scale: [1, 1.1, 1],
        opacity: [0.3, 0.7, 0.3],
        duration: 2500,
        loop: true,
        easing: 'easeInOutSine'
      })
    }

    // Sparkles animation
    if (sparklesRef.current) {
      anime.default({
        targets: sparklesRef.current.children,
        scale: [0, 1, 0],
        opacity: [0, 1, 0],
        rotate: [0, 360],
        duration: 1500,
        delay: anime.default.stagger(200),
        loop: true,
        easing: 'easeInOutSine'
      })
    }

    // Mood-based animations
    const moodAnimations = {
      excited: () => {
        if (headRef.current) {
          anime.default({
            targets: headRef.current,
            scale: [1, 1.05, 1],
            rotate: [-3, 3, -3, 0],
            duration: 600,
            loop: true,
            easing: 'easeInOutElastic(1, .8)'
          })
        }
      },
      celebrating: () => {
        if (svgRef.current) {
          anime.default({
            targets: svgRef.current,
            translateY: [-10, 0],
            scale: [1, 1.1, 1],
            duration: 800,
            loop: true,
            easing: 'easeOutBounce'
          })
        }
      }
    }

    if (moodAnimations[mood]) {
      setTimeout(() => moodAnimations[mood](), 1000)
    }

  }, [mood])

  return (
    <div className={cn('relative inline-block', className)}>
      <svg
        ref={svgRef}
        width={size}
        height={size}
        viewBox="0 0 120 120"
        className="drop-shadow-2xl"
      >
        {/* Definitions for gradients and filters */}
        <defs>
          <radialGradient id="auraGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.2" />
          </radialGradient>
          
          <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FDE68A" />
            <stop offset="100%" stopColor="#F59E0B" />
          </linearGradient>
          
          <linearGradient id="hairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8B5CF6" />
            <stop offset="100%" stopColor="#6366F1" />
          </linearGradient>

          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* Aura */}
        <g ref={auraRef}>
          <circle
            cx="60"
            cy="60"
            r="55"
            fill="url(#auraGradient)"
            filter="url(#glow)"
          />
        </g>

        {/* Sparkles */}
        <g ref={sparklesRef}>
          <circle cx="25" cy="25" r="2" fill="#FCD34D">
            <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite" />
          </circle>
          <circle cx="95" cy="30" r="1.5" fill="#F59E0B">
            <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0.5s" />
          </circle>
          <circle cx="20" cy="85" r="1" fill="#FCD34D">
            <animate attributeName="opacity" values="0;1;0" dur="1.8s" repeatCount="indefinite" begin="1s" />
          </circle>
          <circle cx="100" cy="80" r="2" fill="#F59E0B">
            <animate attributeName="opacity" values="0;1;0" dur="1.3s" repeatCount="indefinite" begin="0.3s" />
          </circle>
        </g>

        {/* Head */}
        <circle
          ref={headRef}
          cx="60"
          cy="60"
          r="35"
          fill="url(#skinGradient)"
          stroke="#F59E0B"
          strokeWidth="2"
        />

        {/* Hair */}
        <g ref={hairRef}>
          <path
            d="M25 45 Q35 25 60 30 Q85 25 95 45 Q90 35 60 25 Q30 35 25 45"
            fill="url(#hairGradient)"
            stroke="#6366F1"
            strokeWidth="1"
          />
          <circle cx="35" cy="40" r="8" fill="url(#hairGradient)" />
          <circle cx="85" cy="40" r="8" fill="url(#hairGradient)" />
        </g>

        {/* Eyes */}
        <g ref={eyesRef}>
          <ellipse cx="50" cy="55" rx="4" ry="6" fill="#1F2937" />
          <ellipse cx="70" cy="55" rx="4" ry="6" fill="#1F2937" />
          <circle cx="51" cy="53" r="1.5" fill="#FFFFFF" />
          <circle cx="71" cy="53" r="1.5" fill="#FFFFFF" />
        </g>

        {/* Nose */}
        <ellipse cx="60" cy="62" rx="1.5" ry="2" fill="#F59E0B" opacity="0.6" />

        {/* Mouth */}
        <path
          d="M55 70 Q60 75 65 70"
          stroke="#1F2937"
          strokeWidth="2"
          fill="none"
          strokeLinecap="round"
        />

        {/* Cheeks */}
        <circle cx="42" cy="65" r="4" fill="#F87171" opacity="0.4" />
        <circle cx="78" cy="65" r="4" fill="#F87171" opacity="0.4" />

        {/* Magical elements */}
        <g>
          <path
            d="M15 15 L20 10 L25 15 L20 20 Z"
            fill="#FCD34D"
            opacity="0.8"
          >
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 20 15;360 20 15"
              dur="4s"
              repeatCount="indefinite"
            />
          </path>
          
          <path
            d="M100 100 L105 95 L110 100 L105 105 Z"
            fill="#8B5CF6"
            opacity="0.8"
          >
            <animateTransform
              attributeName="transform"
              type="rotate"
              values="0 105 100;-360 105 100"
              dur="3s"
              repeatCount="indefinite"
            />
          </path>
        </g>

        {/* Energy waves */}
        <g opacity="0.6">
          <circle cx="60" cy="60" r="40" fill="none" stroke="#8B5CF6" strokeWidth="1">
            <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite" />
            <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite" />
          </circle>
          <circle cx="60" cy="60" r="45" fill="none" stroke="#3B82F6" strokeWidth="1">
            <animate attributeName="r" values="45;50;45" dur="2.5s" repeatCount="indefinite" />
            <animate attributeName="opacity" values="0.4;0.1;0.4" dur="2.5s" repeatCount="indefinite" />
          </circle>
        </g>
      </svg>
    </div>
  )
}

export default SVGAnimatedCharacter
