import React from 'react'
import { motion } from 'framer-motion'
import { Trophy, Target, TrendingUp, Loader2 } from 'lucide-react'
import { cn } from '../../utils/cn'
import type { TaskSetScoreResponse } from '../../services/task/taskService'

interface TaskSetScoreDisplayProps {
  score: TaskSetScoreResponse | null
  loading?: boolean
  className?: string
}

/**
 * Component to display task set score information at the top of task items
 */
const TaskSetScoreDisplay: React.FC<TaskSetScoreDisplayProps> = ({
  score,
  loading = false,
  className
}) => {
  if (loading) {
    return (
      <div className={cn(
        "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",
        className
      )}>
        <div className="flex items-center justify-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
          <span className="text-sm text-blue-600 dark:text-blue-400">Loading task set score...</span>
        </div>
      </div>
    )
  }

  if (!score) {
    return null
  }

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 90) return "text-green-600 dark:text-green-400"
    if (percentage >= 80) return "text-blue-600 dark:text-blue-400"
    if (percentage >= 70) return "text-yellow-600 dark:text-yellow-400"
    if (percentage >= 60) return "text-orange-600 dark:text-orange-400"
    return "text-red-600 dark:text-red-400"
  }

  const getPerformanceBgColor = (percentage: number) => {
    if (percentage >= 90) return "bg-green-100 dark:bg-green-900/30"
    if (percentage >= 80) return "bg-blue-100 dark:bg-blue-900/30"
    if (percentage >= 70) return "bg-yellow-100 dark:bg-yellow-900/30"
    if (percentage >= 60) return "bg-orange-100 dark:bg-orange-900/30"
    return "bg-red-100 dark:bg-red-900/30"
  }

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'Completed'
      case 'in_progress': return 'In Progress'
      case 'not_started': return 'Not Started'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return "text-green-600 dark:text-green-400"
      case 'in_progress': return "text-blue-600 dark:text-blue-400"
      case 'not_started': return "text-gray-600 dark:text-gray-400"
      default: return "text-gray-600 dark:text-gray-400"
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",
        className
      )}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Trophy className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          <span className="text-sm font-semibold text-blue-800 dark:text-blue-300">
            Task Set Score
          </span>
        </div>
        <div className={cn(
          "px-2 py-1 rounded-full text-xs font-medium",
          getPerformanceBgColor(score.percentage),
          getStatusColor(score.status)
        )}>
          {getStatusText(score.status)}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        {/* Total Score */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Target className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-lg font-bold text-slate-900 dark:text-white">
            {score.scored}/{score.total_score}
          </div>
          <div className="text-xs text-slate-600 dark:text-slate-400">
            Total Score
          </div>
        </div>

        {/* Percentage */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className={cn(
            "text-lg font-bold",
            getPerformanceColor(score.percentage)
          )}>
            {Math.round(score.percentage)}%
          </div>
          <div className="text-xs text-slate-600 dark:text-slate-400">
            Accuracy
          </div>
        </div>

        {/* Progress */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Trophy className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-lg font-bold text-slate-900 dark:text-white">
            {score.attempted_tasks}/{score.total_tasks}
          </div>
          <div className="text-xs text-slate-600 dark:text-slate-400">
            Tasks Done
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mt-3">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-slate-600 dark:text-slate-400">
            Overall Progress
          </span>
          <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
            {score.attempted_tasks > 0 ? Math.round((score.attempted_tasks / score.total_tasks) * 100) : 0}%
          </span>
        </div>
        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ 
              width: score.attempted_tasks > 0 
                ? `${(score.attempted_tasks / score.total_tasks) * 100}%` 
                : '0%' 
            }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
          />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetScoreDisplay
