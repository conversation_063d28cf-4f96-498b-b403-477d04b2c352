import React, { useMemo, useRef, useCallback, useState } from 'react'
import { useWavesurfer } from '@wavesurfer/react'
import { Play, Pause, Volume2, VolumeX, SkipBack, SkipForward } from 'lucide-react'
import { cn } from '../../utils/cn'

interface SimpleAudioPlayerProps {
  src: string
  title?: string
  className?: string
}

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(1, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * Simple Audio Player using WaveSurfer.js
 * Matches the reference UI with controls at the bottom
 */
const SimpleAudioPlayer: React.FC<SimpleAudioPlayerProps> = ({
  src,
  title,
  className
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)

  const { wavesurfer } = useWavesurfer({
    container: containerRef,
    height: 80,
    waveColor: 'rgb(200, 0, 200)',
    progressColor: 'rgb(100, 0, 100)',
    cursorColor: '#ddd5e9',
    url: src,
    mediaControls: false, // We'll use custom controls
    dragToSeek: true,
    interact: true,
    normalize: true,
    autoScroll: true,
    autoCenter: true,
    sampleRate: 8000,
    hideScrollbar: false,
    plugins: useMemo(() => [], []),
  })

  // Set up event listeners
  React.useEffect(() => {
    if (wavesurfer) {
      const handleReady = () => {
        setDuration(wavesurfer.getDuration())
        wavesurfer.setTime(0) // Set time to 0 seconds (beginning) when ready
      }

      const handlePlay = () => setIsPlaying(true)
      const handlePause = () => setIsPlaying(false)
      const handleTimeUpdate = (time: number) => setCurrentTime(time)

      wavesurfer.on('ready', handleReady)
      wavesurfer.on('play', handlePlay)
      wavesurfer.on('pause', handlePause)
      wavesurfer.on('timeupdate', handleTimeUpdate)

      return () => {
        wavesurfer.un('ready', handleReady)
        wavesurfer.un('play', handlePlay)
        wavesurfer.un('pause', handlePause)
        wavesurfer.un('timeupdate', handleTimeUpdate)
      }
    }
  }, [wavesurfer])

  // Control functions
  const togglePlayPause = useCallback(() => {
    if (wavesurfer) {
      wavesurfer.playPause()
    }
  }, [wavesurfer])

  const skipBackward = useCallback(() => {
    if (wavesurfer) {
      const newTime = Math.max(0, currentTime - 10)
      wavesurfer.setTime(newTime)
    }
  }, [wavesurfer, currentTime])

  const skipForward = useCallback(() => {
    if (wavesurfer) {
      const newTime = Math.min(duration, currentTime + 10)
      wavesurfer.setTime(newTime)
    }
  }, [wavesurfer, currentTime, duration])

  const toggleMute = useCallback(() => {
    if (wavesurfer) {
      const newMuted = !isMuted
      setIsMuted(newMuted)
      wavesurfer.setVolume(newMuted ? 0 : volume)
    }
  }, [wavesurfer, isMuted, volume])

  const handleVolumeChange = useCallback((newVolume: number) => {
    if (wavesurfer) {
      setVolume(newVolume)
      if (!isMuted) {
        wavesurfer.setVolume(newVolume)
      }
    }
  }, [wavesurfer, isMuted])

  // Add custom CSS for modern slider styling
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .slider {
        background: hsl(var(--muted));
        outline: none;
        -webkit-appearance: none;
      }

      .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: hsl(var(--primary));
        cursor: pointer;
        border: 2px solid hsl(var(--background));
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .slider::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: hsl(var(--primary));
        cursor: pointer;
        border: 2px solid hsl(var(--background));
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  return (
    <div className={cn("bg-card border border-border rounded-xl p-3 sm:p-4 lg:p-6 shadow-lg", className)}>
      {/* Title */}
      {title && (
        <div className="mb-3 sm:mb-4">
          <h3 className="font-semibold text-base sm:text-lg text-card-foreground truncate">{title}</h3>
        </div>
      )}

      {/* Waveform */}
      <div className="mb-3 sm:mb-4">
        <div ref={containerRef} className="rounded-lg overflow-hidden bg-muted/30" />
      </div>

      {/* Custom Controls */}
      <div className="space-y-3 sm:space-y-4">
        {/* Time Display */}
        <div className="flex justify-between items-center text-xs sm:text-sm text-muted-foreground font-mono">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>

        {/* Control Layout - Play controls and Volume */}
        <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4">
          {/* Play Controls - Left/Center */}
          <div className="flex items-center justify-center space-x-3 sm:space-x-4 flex-1">
            <button
              onClick={skipBackward}
              className="p-1.5 sm:p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors"
              title="Skip back 10s"
            >
              <SkipBack className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>

            <button
              onClick={togglePlayPause}
              className="p-2.5 sm:p-3 rounded-full bg-primary hover:bg-primary/90 text-primary-foreground transition-colors shadow-lg"
              title={isPlaying ? "Pause" : "Play"}
            >
              {isPlaying ? (
                <Pause className="w-5 h-5 sm:w-6 sm:h-6" />
              ) : (
                <Play className="w-5 h-5 sm:w-6 sm:h-6 ml-0.5" />
              )}
            </button>

            <button
              onClick={skipForward}
              className="p-1.5 sm:p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors"
              title="Skip forward 10s"
            >
              <SkipForward className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>

          {/* Volume Control - Right Side */}
          <div className="flex items-center space-x-2 min-w-0 w-full sm:w-32 lg:w-40">
            <button
              onClick={toggleMute}
              className="p-1 sm:p-1.5 rounded-md hover:bg-muted transition-colors flex-shrink-0"
              title={isMuted ? "Unmute" : "Mute"}
            >
              {isMuted ? (
                <VolumeX className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
              ) : (
                <Volume2 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
              )}
            </button>

            <div className="flex-1 min-w-0">
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="w-full h-1.5 sm:h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SimpleAudioPlayer
