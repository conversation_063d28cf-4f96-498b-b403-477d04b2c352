import React, { useState, useRef, useEffect } from 'react';
import { Mic, Square, Play, StopCircle, Loader2, CheckCircle, XCircle } from 'lucide-react';

interface AudioRecorderProps {
  onRecordingComplete: (audioBlob: Blob) => void;
  disabled?: boolean;
  maxDuration?: number; // in seconds
  initialAudioUrl?: string | null;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onRecordingComplete,
  disabled = false,
  maxDuration = 30,
  initialAudioUrl = null,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordedAudio, setRecordedAudio] = useState<string | null>(initialAudioUrl);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<number | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Initialize audio element
  useEffect(() => {
    if (typeof window !== 'undefined') {
      audioElementRef.current = new Audio();
      audioElementRef.current.onended = () => setIsPlaying(false);
    }
    
    return () => {
      if (audioElementRef.current) {
        audioElementRef.current.pause();
        audioElementRef.current = null;
      }
      stopRecording();
    };
  }, []);

  // Update initial audio URL if it changes
  useEffect(() => {
    setRecordedAudio(initialAudioUrl);
  }, [initialAudioUrl]);

  const startRecording = async () => {
    try {
      setError(null);
      audioChunksRef.current = [];
      
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setRecordedAudio(audioUrl);
        onRecordingComplete(audioBlob);
      };
      
      mediaRecorder.start();
      setIsRecording(true);
      setElapsedTime(0);
      
      // Start timer
      if (timerRef.current) window.clearInterval(timerRef.current);
      timerRef.current = window.setInterval(() => {
        setElapsedTime((prev) => {
          if (prev >= maxDuration) {
            stopRecording();
            return maxDuration;
          }
          return prev + 1;
        });
      }, 1000);
      
    } catch (err) {
      console.error('Error accessing microphone:', err);
      setError('Could not access microphone. Please check your permissions.');
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
      
      // Stop all tracks in the stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      
      setIsRecording(false);
    }
  };

  const togglePlayback = () => {
    if (!audioElementRef.current || !recordedAudio) return;
    
    if (isPlaying) {
      audioElementRef.current.pause();
      setIsPlaying(false);
    } else {
      audioElementRef.current.src = recordedAudio;
      audioElementRef.current.play()
        .then(() => setIsPlaying(true))
        .catch(err => {
          console.error('Error playing audio:', err);
          setError('Could not play audio');
        });
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleRetry = () => {
    setRecordedAudio(null);
    setError(null);
    setElapsedTime(0);
  };

  return (
    <div className="w-full space-y-4">
      {/* Recording/Playback Controls */}
      <div className="flex flex-col items-center gap-4">
        {!recordedAudio ? (
          <div className="flex flex-col items-center gap-2">
            <button
              onClick={isRecording ? stopRecording : startRecording}
              disabled={disabled}
              className={`
                rounded-full h-16 w-16 p-0 relative flex items-center justify-center
                ${isRecording 
                  ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' 
                  : 'bg-primary text-primary-foreground hover:bg-primary/90'}
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors duration-200
              `}
            >
              {isRecording ? (
                <Square className="h-6 w-6" />
              ) : (
                <Mic className="h-6 w-6" />
              )}
            </button>
            <p className="text-sm text-muted-foreground">
              {isRecording ? 'Stop Recording' : 'Tap to Record'}
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-3 w-full">
            <div className="flex items-center gap-4">
              <button
                onClick={togglePlayback}
                disabled={!recordedAudio}
                className={`
                  rounded-full h-12 w-12 flex items-center justify-center
                  border border-input bg-background hover:bg-accent hover:text-accent-foreground
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-colors duration-200
                `}
              >
                {isPlaying ? (
                  <StopCircle className="h-5 w-5" />
                ) : (
                  <Play className="h-5 w-5" />
                )}
              </button>
              <div className="text-center">
                <div className="text-2xl font-mono">
                  {formatTime(isRecording ? elapsedTime : (audioElementRef.current?.duration || 0))}
                </div>
                <p className="text-xs text-muted-foreground">
                  {isRecording ? 'Recording...' : 'Your recording'}
                </p>
              </div>
            </div>
            <button
              onClick={handleRetry}
              disabled={disabled}
              className={`
                mt-2 px-3 py-1.5 text-sm rounded-md
                border border-input bg-background hover:bg-accent hover:text-accent-foreground
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors duration-200
                flex items-center justify-center
              `}
            >
              <Mic className="h-4 w-4 mr-2" />
              Record Again
            </button>
          </div>
        )}
      </div>

      {/* Status Indicators */}
      <div className="text-center">
        {isRecording && (
          <div className="flex items-center justify-center gap-2 text-sm text-amber-600 dark:text-amber-400">
            <div className="flex space-x-1 items-center">
              <div className="w-2 h-2 bg-amber-600 dark:bg-amber-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-amber-600 dark:bg-amber-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-2 h-2 bg-amber-600 dark:bg-amber-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
            <span>Recording in progress...</span>
          </div>
        )}
        
        {error && (
          <div className="text-sm text-red-600 dark:text-red-400 flex items-center justify-center gap-2">
            <XCircle className="h-4 w-4" />
            {error}
          </div>
        )}
        
        {recordedAudio && !isRecording && !error && (
          <div className="text-sm text-green-600 dark:text-green-400 flex items-center justify-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Recording saved
          </div>
        )}
      </div>
      
      {/* Hidden audio element for playback */}
      <audio ref={audioElementRef} />
    </div>
  );
};

export default AudioRecorder;
