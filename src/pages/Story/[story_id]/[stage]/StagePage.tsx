import React, { useState, useRef, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  RotateCcw,
  AlertCircle
} from 'lucide-react'
import { useStoryCache } from '../../../../hooks/useStoryCache'

const StagePage: React.FC = () => {
  const { story_id } = useParams<{ story_id: string }>()
  const [searchParams] = useSearchParams()
  const currentStage = parseInt(searchParams.get('stage') || '1', 10)
  
  const {
    storyData,
    isStageLoading,
    error,
    retryCurrentStage
  } = useStoryCache(story_id || '', currentStage)

  // Audio player state
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const audioRef = useRef<HTMLAudioElement>(null)

  // Audio player handlers
  const handlePlayPause = () => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleMuteToggle = () => {
    if (!audioRef.current) return
    
    audioRef.current.muted = !isMuted
    setIsMuted(!isMuted)
  }

  const handleRestart = () => {
    if (!audioRef.current) return
    
    audioRef.current.currentTime = 0
    setCurrentTime(0)
  }

  const handleTimeUpdate = () => {
    if (!audioRef.current) return
    
    setCurrentTime(audioRef.current.currentTime)
  }

  const handleLoadedMetadata = () => {
    if (!audioRef.current) return
    
    setDuration(audioRef.current.duration)
  }

  const handleEnded = () => {
    setIsPlaying(false)
    setCurrentTime(0)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Reset audio state when stage changes
  useEffect(() => {
    setIsPlaying(false)
    setCurrentTime(0)
    setDuration(0)
  }, [currentStage])

  // YouTube-style skeleton with heartbeat animation
  const SkeletonLoader = () => (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="h-full bg-white"
    >
      <div className="max-w-6xl mx-auto h-full p-6">
        <div className="bg-white rounded-2xl shadow-xl border border-slate-200 h-full flex flex-col overflow-hidden">

          {/* Content Skeleton with Heartbeat */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <div className="h-full flex flex-col p-6">
              {/* Image skeleton with shimmer effect */}
              <div className="flex-1 min-h-0 mb-6 relative overflow-hidden">
                <div className="w-full h-full bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded-xl relative">
                  {/* Shimmer overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer rounded-xl"></div>
                  {/* Heartbeat pulse */}
                  <div className="absolute inset-0 bg-slate-300/30 rounded-xl animate-heartbeat"></div>
                </div>
              </div>

              {/* Text content skeleton with staggered animation */}
              <div className="shrink-0 bg-slate-50 rounded-xl p-6 relative overflow-hidden">
                {/* Title skeleton */}
                <div className="relative mb-4">
                  <div className="h-8 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded-lg w-1/3 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer-slow"></div>
                  </div>
                </div>

                {/* Text lines with staggered heartbeat */}
                <div className="space-y-3">
                  {[100, 85, 90, 75].map((width, index) => (
                    <div key={index} className="relative">
                      <div
                        className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded relative overflow-hidden"
                        style={{
                          width: `${width}%`,
                          animationDelay: `${index * 0.2}s`
                        }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer"></div>
                        <div className="absolute inset-0 bg-slate-300/20 rounded animate-heartbeat-slow"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Audio player skeleton with pulse */}
          <div className="bg-gradient-to-r from-slate-50 to-slate-100 p-6 border-t border-slate-200 shrink-0">
            <div className="flex items-center gap-4">
              {/* Play button skeleton */}
              <div className="w-14 h-14 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded-full relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer rounded-full"></div>
                <div className="absolute inset-0 bg-slate-300/30 rounded-full animate-heartbeat"></div>
              </div>

              {/* Progress area skeleton */}
              <div className="flex-1">
                <div className="flex items-center justify-between text-sm mb-2">
                  <div className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded w-24 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer-slow"></div>
                  </div>
                  <div className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded w-16 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer-slow"></div>
                  </div>
                </div>
                <div className="w-full bg-slate-300 rounded-full h-2 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer"></div>
                  <div className="absolute left-0 top-0 h-full w-1/3 bg-slate-400/50 rounded-full animate-pulse"></div>
                </div>
              </div>

              {/* Control buttons skeleton */}
              <div className="flex items-center gap-2">
                {[1, 2].map((i) => (
                  <div key={i} className="w-10 h-10 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded-full relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent animate-shimmer rounded-full"></div>
                    <div
                      className="absolute inset-0 bg-slate-300/20 rounded-full animate-heartbeat-slow"
                      style={{ animationDelay: `${i * 0.3}s` }}
                    ></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )

  if (isStageLoading || !storyData) {
    return <SkeletonLoader />
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="h-full flex items-center justify-center bg-white"
      >
        <div className="text-center p-8 max-w-md">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-slate-900 mb-2">Chapter Unavailable</h3>
          <p className="text-slate-600 mb-6">{error}</p>
          <button
            onClick={retryCurrentStage}
            className="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-all duration-200 font-medium"
          >
            Try Again
          </button>
        </div>
      </motion.div>
    )
  }



  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="h-full bg-white"
    >
      <div className="max-w-6xl mx-auto h-full p-6">
        <div className="bg-white rounded-2xl shadow-xl border border-slate-200 h-full flex flex-col overflow-hidden">
          
          {/* Content Area */}
          <div className="flex-1 min-h-0 overflow-hidden">
            {storyData.step.media?.url ? (
              // Comic book style with background image
              <motion.div
                initial={{ opacity: 0, scale: 0.98 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, ease: "easeOut" }}
                className="w-full h-full relative overflow-hidden rounded-t-2xl"
                style={{
                  backgroundImage: `url(${storyData.step.media.url})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent flex items-end">
                  <div className="p-8 w-full">
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.5, ease: "easeOut" }}
                      className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl border border-white/50"
                    >
                      <h2 className="text-2xl font-bold text-slate-900 mb-4">
                        Chapter {currentStage}
                      </h2>
                      <p className="text-slate-800 leading-relaxed text-lg font-medium">
                        {storyData.step.script}
                      </p>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ) : storyData.step.image ? (
              // Traditional layout with separate image and text
              <div className="h-full flex flex-col p-6">
                <div className="flex-1 min-h-0 mb-6">
                  <motion.img
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                    src={storyData.step.image}
                    alt={`Story chapter ${currentStage}`}
                    className="w-full h-full object-contain rounded-xl"
                  />
                </div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.5, ease: "easeOut" }}
                  className="shrink-0 bg-slate-50 rounded-xl p-6"
                >
                  <h2 className="text-2xl font-bold text-slate-900 mb-4">
                    Chapter {currentStage}
                  </h2>
                  <p className="text-slate-700 leading-relaxed text-lg">
                    {storyData.step.script}
                  </p>
                </motion.div>
              </div>
            ) : (
              // Text-only layout
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, ease: "easeOut" }}
                className="h-full flex flex-col justify-center text-center p-8"
              >
                <motion.h2
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.4 }}
                  className="text-3xl font-bold text-slate-900 mb-8"
                >
                  Chapter {currentStage}
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.4 }}
                  className="text-xl text-slate-700 leading-relaxed max-w-4xl mx-auto"
                >
                  {storyData.step.script}
                </motion.p>
              </motion.div>
            )}
          </div>

          {/* Enhanced Audio Player */}
          {storyData.step.audio_url && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5, ease: "easeOut" }}
              className="bg-gradient-to-r from-slate-50 to-slate-100 p-6 border-t border-slate-200 shrink-0"
            >
              <div className="flex items-center gap-4">
                <button
                  onClick={handlePlayPause}
                  className="flex items-center justify-center w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full hover:from-blue-600 hover:to-purple-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {isPlaying ? (
                    <Pause className="w-6 h-6" />
                  ) : (
                    <Play className="w-6 h-6 ml-1" />
                  )}
                </button>

                <div className="flex-1">
                  <div className="flex items-center justify-between text-sm text-slate-600 mb-2">
                    <span>Audio Narration</span>
                    <span>{formatTime(currentTime)} / {formatTime(duration)}</span>
                  </div>
                  <div className="w-full bg-slate-300 rounded-full h-2 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-200"
                      style={{ width: duration ? `${(currentTime / duration) * 100}%` : '0%' }}
                    />
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={handleRestart}
                    className="flex items-center justify-center w-10 h-10 bg-slate-200 hover:bg-slate-300 text-slate-700 rounded-full transition-all duration-200"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                  <button
                    onClick={handleMuteToggle}
                    className="flex items-center justify-center w-10 h-10 bg-slate-200 hover:bg-slate-300 text-slate-700 rounded-full transition-all duration-200"
                  >
                    {isMuted ? (
                      <VolumeX className="w-4 h-4" />
                    ) : (
                      <Volume2 className="w-4 h-4" />
                    )}
                  </button>
                </div>

                <audio
                  ref={audioRef}
                  src={storyData.step.audio_url}
                  onTimeUpdate={handleTimeUpdate}
                  onLoadedMetadata={handleLoadedMetadata}
                  onEnded={handleEnded}
                  preload="metadata"
                />
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default StagePage
