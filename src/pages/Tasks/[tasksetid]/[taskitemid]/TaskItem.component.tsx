import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import TaskTypeBadge from '../../../../components/task/TaskTypeBadge'
import {
  Loader2,
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Languages,
  Expand,
  X,
  HelpCircle,
  Mic, Volume2, BookOpen, Check, Lightbulb
} from 'lucide-react'
import { Link } from 'react-router-dom'
import MainLayout from '../../../../components/layout/MainLayout'
import { TaskItemSkeleton } from '../../../../components/ui/Skeleton'
import TaskSetScoreDisplay from '../../../../components/ui/TaskSetScoreDisplay'
import { cn } from '../../../../utils/cn'
import AudioRecorder from '../../../../components/audio/AudioRecorder';

// Types for question object structure (matching old TaskItem)
interface QuestionMetadata {
  object_name: string
  bucket_name: string
  object_path: string
  file_name: string
  content_type: string
  size_bytes: number
  folder: string
  session_id: string
  created_at: string
  file_extension: string
  url: string
}

interface QuestionObject {
  text: string
  translated_text?: string
  options: {
    [key: string]: string // e.g., { a: "Option 1", b: "Option 2", c: "Option 3" }
  }
  media_url?: string | null
  metadata?: QuestionMetadata
  answer_hint?: string
  options_metadata?: {
    [key: string]: {
      text: string
      audio_url?: string
      file_info?: {
        object_name: string
        bucket_name: string
        object_path: string
        file_name: string
        url: string
        content_type: string
        size_bytes: number
        user_id: string
        folder: string
        session_id: string | null
        created_at: string
        file_extension: string
      }
      usage_metadata?: any
      generated_at?: string
    }
  }
}

interface TaskItemComponentProps {
  taskSetId: string
  taskItemId: string
  taskSet: any | null
  taskItem: any | null
  taskSetScore: any | null
  loading: boolean
  loadingScore?: boolean
  error: string | null
  submitting: boolean
  currentTaskIndex: number
  totalTasks: number
  onSubmitAnswer: (answer: { selected_option?: string; selected_options?: string[]; text_answer?: string }) => void
  onNavigateTask: (direction: 'next' | 'prev') => void
  onViewStories: () => void
  onRefresh: () => void
}

/**
 * TaskItem Component - Pure UI component for individual task item page
 */
const TaskItemComponent: React.FC<TaskItemComponentProps> = ({
  taskSetId,
  taskItem,
  taskSetScore,
  loading,
  loadingScore = false,
  error,
  submitting,
  currentTaskIndex,
  totalTasks,
  onSubmitAnswer,
  onNavigateTask,
  onViewStories
}) => {
  const [selectedAnswer, setSelectedAnswer] = useState<string>('')
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([])
  const [textAnswer, setTextAnswer] = useState<string>('')
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [showTranslation, setShowTranslation] = useState<boolean>(false)
  const [showImagePreview, setShowImagePreview] = useState<boolean>(false)
  const [showHint, setShowHint] = useState<boolean>(false)
  const [playingAudio, setPlayingAudio] = useState<string | null>(null)


  // Add animation key for content transitions
  const [contentKey, setContentKey] = useState<string>('')

  // Reset form states when navigating to a new task (NO AUTO STORY DISPLAY)
  useEffect(() => {
    // Reset form states when navigating to a new task
    setSelectedAnswer('')
    setSelectedAnswers([])
    setTextAnswer('')
    setShowTranslation(false)
    setShowImagePreview(false)
    setShowHint(false)

    // Update content key to trigger animation
    setContentKey(taskItem?.id || taskItem?._id || '')
  }, [taskItem?.id, taskItem?._id]) // Reset when task ID changes (navigation)

  // Auto-hide hint after 3 seconds
  useEffect(() => {
    if (showHint) {
      const timer = setTimeout(() => {
        setShowHint(false)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [showHint])



  // Set initial values when task item loads or changes
  useEffect(() => {
    if (taskItem) {
      // Set selected answer if task is already answered
      if (taskItem.type === 'multiple_choice' || taskItem.type === 'MULTIPLE_CHOICE') {
        // Handle multiple choice answers
        if (taskItem.user_answer?.selected_options && Array.isArray(taskItem.user_answer.selected_options)) {
          setSelectedAnswers(taskItem.user_answer.selected_options)
        } else if (taskItem.user_answer && Array.isArray(taskItem.user_answer)) {
          setSelectedAnswers(taskItem.user_answer)
        }
      } else {
        // Handle single choice answers
        if (taskItem.user_answer?.selected_option) {
          setSelectedAnswer(taskItem.user_answer.selected_option)
        } else if (taskItem.user_answer && typeof taskItem.user_answer === 'string') {
          setSelectedAnswer(taskItem.user_answer)
        }
      }

      // Set text answer if available
      if (taskItem.user_answer?.text_answer) {
        setTextAnswer(taskItem.user_answer.text_answer)
      }

      // Set audio URL if available (for speak_word tasks)
      if (taskItem.type === 'speak_word' && taskItem.user_answer?.audio_url) {
        // We'll handle the audio URL in the AudioRecorder component directly
      }
    }
  }, [taskItem])

  // Helper functions for question object handling (from old TaskItem)
  const isQuestionObject = (question: string | QuestionObject | undefined): question is QuestionObject => {
    return typeof question === 'object' && question !== null && 'text' in question
  }

  const getQuestionText = (): string => {
    if (!taskItem?.question) return ''

    if (isQuestionObject(taskItem.question)) {
      return taskItem.question.text
    }

    return taskItem.question
  }

  const getQuestionOptionsWithKeys = (): Array<{key: string, value: string}> => {
    // Check if question is an object with options
    if (isQuestionObject(taskItem?.question) && taskItem.question.options) {
      // Return array of {key, value} pairs for new question format
      const options = taskItem.question.options
      return Object.keys(options).sort().map(key => ({
        key,
        value: options[key]
      }))
    }

    // For legacy format, create keys as indices
    const legacyOptions = taskItem?.options || []
    return legacyOptions.map((value: string, index: number) => ({
      key: String.fromCharCode(97 + index), // 'a', 'b', 'c', etc.
      value
    }))
  }

  const getTranslatedText = (): string => {
    if (isQuestionObject(taskItem?.question)) {
      return taskItem.question.translated_text || ''
    }
    return ''
  }

  const hasTranslation = (): boolean => {
    return getTranslatedText().length > 0
  }

  const getMediaUrl = (): string => {
    // Check question.metadata.url first (new format with metadata object)
    if (isQuestionObject(taskItem?.question) && taskItem.question.metadata?.url) {
      return taskItem.question.metadata.url
    }

    // Check question.media_url (for image identification tasks)
    if (isQuestionObject(taskItem?.question) && taskItem.question.media_url) {
      return taskItem.question.media_url
    }

    // Fallback to task.media_url
    return taskItem?.media_url || ''
  }

  const getQuestionTypeHint = (type: string): string => {
    const hints: Record<string, string> = {
      'single_choice': 'Select one correct answer from the options',
      'multiple_choice': 'Select all correct answers from the options',
      'image_identification': 'Look at the image and select the correct answer',
      'text_input': 'Type your answer in the text field',
      'speak_word': 'Record yourself speaking the word',
      'answer_in_word': 'Provide a single word answer'
    }
    return hints[type.toLowerCase()] || 'Complete this task'
  }

  // Get answer hint from question object
  const getAnswerHint = (): string => {
    if (isQuestionObject(taskItem?.question) && taskItem.question.answer_hint) {
      return taskItem.question.answer_hint
    }
    return ''
  }

  // Check if hint is available for this question type
  const hasHint = (): boolean => {
    const questionType = taskItem?.type?.toLowerCase()
    return (questionType === 'single_choice' || questionType === 'multiple_choice') && getAnswerHint().length > 0
  }

  // Get option metadata for audio playback
  const getOptionMetadata = (optionKey: string) => {
    if (isQuestionObject(taskItem?.question) && taskItem.question.options_metadata) {
      return taskItem.question.options_metadata[optionKey]
    }
    return null
  }

  // Play audio for option when clicked
  const playOptionAudio = (optionKey: string) => {
    const optionMetadata = getOptionMetadata(optionKey)
    if (!optionMetadata?.audio_url) return

    // Stop any currently playing audio
    if (playingAudio) {
      const currentAudio = document.getElementById(`option-audio-${playingAudio}`) as HTMLAudioElement
      if (currentAudio) {
        currentAudio.pause()
        currentAudio.currentTime = 0
      }
    }

    // Play new audio
    setPlayingAudio(optionKey)
    const audioElement = document.getElementById(`option-audio-${optionKey}`) as HTMLAudioElement
    if (audioElement) {
      audioElement.play().catch(console.error)
    }
  }

  const handleSubmit = async () => {
    if (!taskItem) return

    const answer: {
      selected_option?: string
      selected_options?: string[]
      text_answer?: string
      audio_blob?: Blob
    } = {}

    try {
      if (taskItem.type === 'multiple_choice' || taskItem.type === 'MULTIPLE_CHOICE') {
        // Handle multiple choice - use selected_options array
        answer.selected_options = selectedAnswers
      } else if (taskItem.type === 'single_choice' || taskItem.type === 'image_identification' ||
          taskItem.type === 'SINGLE_CHOICE' || taskItem.type === 'IMAGE_IDENTIFICATION') {
        // Handle single choice - use selected_option string
        answer.selected_option = selectedAnswer
      } else if (taskItem.type === 'text_input' || taskItem.type === 'TEXT_INPUT') {
        answer.text_answer = textAnswer
      } else if (taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD') {
        if (!audioBlob && !taskItem.user_answer?.audio_url) {
          // Show error if no audio is recorded
          return
        }
        // Pass the audio blob to be handled by the container
        answer.audio_blob = audioBlob || undefined
      }

      onSubmitAnswer(answer)
    } catch (error) {
      console.error('Error submitting answer:', error)
    }
  }

  // Handle single choice selection using option keys
  const handleSingleChoiceSelect = (optionKey: string) => {
    if (submitting || isAnswered) return
    setSelectedAnswer(optionKey)
    // Play audio when option is selected
    playOptionAudio(optionKey)
  }

  // Handle multiple choice selection using option keys
  const handleMultipleChoiceSelect = (optionKey: string) => {
    if (submitting || isAnswered) return
    setSelectedAnswers(prev => {
      if (prev.includes(optionKey)) {
        // Remove if already selected
        return prev.filter(key => key !== optionKey)
      } else {
        // Add if not selected
        return [...prev, optionKey]
      }
    })
    // Play audio when option is clicked (selected or deselected)
    playOptionAudio(optionKey)
  }

  // Check multiple ways a task can be considered answered (for compatibility)
  const isAnswered = taskItem?.status === 'COMPLETED' || taskItem?.submitted === true
  const isCorrect = taskItem?.result === 'CORRECT' || taskItem?.result === 'correct'

  return (
    <MainLayout
      title={taskItem ? getQuestionText() || 'Task Item' : 'Task Item'}
      description="Complete this task item"
    >
      <div className="h-[calc(100vh-4rem)] flex flex-col max-w-4xl mx-auto">
        {/* Compact Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b border-border">
          <Link
            to={`/tasks/${taskSetId}`}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Previous</span>
          </Link>

          {/* Task Progress & Navigation */}
          <div className="flex items-center gap-4">
            {taskItem && (
              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                <div className="flex items-center gap-1" title={getQuestionTypeHint(taskItem.type || '')}>
                  <span className="capitalize">{taskItem.type?.replace('_', ' ').toLowerCase()}</span>
                  <HelpCircle className="h-3 w-3" />
                </div>
                <span>•</span>
                <span>Score: {taskItem.scored || 0}/{taskItem.total_score || 0}</span>
                <span>•</span>
                <span>Task {currentTaskIndex + 1} of {totalTasks}</span>

                {isAnswered && (
                  <>
                    <span>•</span>
                    <span className={cn(
                      "flex items-center gap-1 px-2 py-1 rounded-full text-xs",
                      isCorrect ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                    )}>
                      {isCorrect ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
                      {isCorrect ? 'Correct' : 'Incorrect'}
                    </span>
                  </>
                )}
              </div>
            )}

            <div className="flex items-center gap-1">
              <button
                onClick={() => onNavigateTask('prev')}
                disabled={currentTaskIndex <= 0}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  currentTaskIndex <= 0
                    ? "cursor-not-allowed opacity-50"
                    : "hover:bg-accent"
                )}
                title={currentTaskIndex <= 0 ? "No previous task" : "Previous Task"}
              >
                <ChevronLeft className="h-4 w-4 text-muted-foreground" />
              </button>

              {/* Show "View Stories" button instead of disabled next button on last task */}
              {currentTaskIndex >= totalTasks - 1 ? (
                <button
                  onClick={onViewStories}
                  className="flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors text-sm"
                  title="View Stories"
                >
                  <BookOpen className="h-4 w-4" />
                  View Stories
                </button>
              ) : (
                <button
                  onClick={() => onNavigateTask('next')}
                  className="p-2 rounded-lg transition-colors hover:bg-accent"
                  title="Next Task"
                >
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Error state */}
        {error && (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 max-w-md">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-destructive" />
                <div>
                  <h3 className="font-medium text-destructive">Error</h3>
                  <p className="text-sm text-destructive/80">{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Loading state */}
        {loading ? (
          <TaskItemSkeleton />
        ) : taskItem ? (
          <div className="flex-1 p-4 space-y-4 overflow-y-auto">
            {/* Task Set Score Display */}
            <TaskSetScoreDisplay
              score={taskSetScore}
              loading={loadingScore}
              className="mb-4"
            />



            {/* Question Section */}
            <AnimatePresence mode="wait">
              <motion.div
                key={contentKey} // This will trigger re-animation when content changes
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={cn(
                    "text-center p-6 rounded-xl border-2 transition-all duration-300",
                    isAnswered
                      ? isCorrect
                        ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 shadow-green-100 dark:shadow-green-900/20"
                        : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 shadow-red-100 dark:shadow-red-900/20"
                      : "bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 shadow-lg hover:shadow-xl"
                  )}
                >
              {/* Status indicator */}
              {isAnswered && (
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className={cn(
                    "inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium mb-4",
                    isCorrect
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                  )}
                >
                  {isCorrect ? (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      Correct Answer!
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4" />
                      Incorrect Answer
                    </>
                  )}
                </motion.div>
              )}

              <div className="flex flex-col items-center gap-3 mb-4">
                <div className="flex items-center gap-3">
                  <h1 className={cn(
                    "text-2xl font-bold transition-colors",
                    isAnswered
                      ? isCorrect
                        ? "text-green-800 dark:text-green-200"
                        : "text-red-800 dark:text-red-200"
                      : "text-foreground"
                  )}>
                    {getQuestionText()}
                  </h1>
                </div>
                {taskItem?.type && (
                  <div className="flex items-center gap-2">
                    <TaskTypeBadge type={taskItem.type} />
                    <span className="text-xs text-muted-foreground">
                      Question {currentTaskIndex + 1} of {totalTasks}
                    </span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  {hasTranslation() && (
                    <button
                      onClick={() => setShowTranslation(!showTranslation)}
                      className="flex items-center gap-1 px-3 py-1 text-sm border border-border rounded-lg hover:bg-accent transition-colors"
                      title="Toggle translation"
                    >
                      <Languages className="h-4 w-4" />
                      {showTranslation ? 'Original' : 'Translate'}
                    </button>
                  )}

                  {hasHint() && !isAnswered && (
                    <button
                      onClick={() => setShowHint(true)}
                      className="flex items-center gap-1 px-3 py-1 text-sm border border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 rounded-lg hover:bg-amber-100 dark:hover:bg-amber-900/40 transition-colors"
                      title="Show hint"
                    >
                      <Lightbulb className="h-4 w-4" />
                      Hint
                    </button>
                  )}
                </div>
              </div>

              {showTranslation && hasTranslation() && (
                <p className="text-muted-foreground mb-3 p-3 bg-muted rounded-lg max-w-2xl mx-auto">
                  <span className="text-xs font-medium text-muted-foreground/70 uppercase tracking-wide">Translation:</span><br />
                  {getTranslatedText()}
                </p>
              )}

              {/* Hint Display */}
              {showHint && hasHint() && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="mb-3 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg max-w-2xl mx-auto"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Lightbulb className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    <span className="text-xs font-medium text-amber-600 dark:text-amber-400 uppercase tracking-wide">Hint:</span>
                  </div>
                  <p className="text-amber-800 dark:text-amber-200 font-medium">
                    {getAnswerHint()}
                  </p>
                  <div className="mt-2 text-xs text-amber-600 dark:text-amber-400">
                    This hint will disappear in 3 seconds...
                  </div>
                </motion.div>
              )}
            </motion.div>

            {/* Media Content */}
            {(() => {
              const mediaUrl = getMediaUrl()
              const metadata = isQuestionObject(taskItem?.question) ? taskItem.question.metadata : null
              const isAudio = metadata?.content_type?.startsWith('audio/') || mediaUrl?.toLowerCase().endsWith('.wav') || mediaUrl?.toLowerCase().endsWith('.mp3')
              const isImage = !isAudio && (metadata?.content_type?.startsWith('image/') || 
                mediaUrl?.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/))

              if (mediaUrl) {
                return (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex justify-center"
                  >
                    {isAudio ? (
                      <div className="w-full max-w-md bg-white dark:bg-slate-800 p-4 rounded-xl shadow-lg">
                        <div className="flex items-center justify-center gap-3 mb-2">
                          <Volume2 className="h-6 w-6 text-blue-600" />
                          <h3 className="text-lg font-medium">Listen to the audio</h3>
                        </div>
                        <audio 
                          src={mediaUrl} 
                          controls 
                          className="w-full mt-2"
                        >
                          Your browser does not support the audio element.
                        </audio>
                      </div>
                    ) : isImage ? (
                      <div className="relative group">
                        <img
                          src={mediaUrl}
                          alt="Task media"
                          className="max-w-sm max-h-80 object-contain rounded-lg shadow-lg cursor-pointer transition-transform hover:scale-105"
                          onClick={() => setShowImagePreview(true)}
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors flex items-center justify-center">
                          <Expand className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                        <div className="absolute top-2 right-2 bg-black/60 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                          Click to expand
                        </div>
                      </div>
                    ) : (
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                          Unsupported media type: {metadata?.content_type || 'Unknown'}
                        </p>
                      </div>
                    )}
                  </motion.div>
                )
              }
              return null
            })()}

            {/* Answer Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-2xl mx-auto"
            >

              {/* Single/Multiple Choice and Image Identification Options */}
              {(taskItem.type === 'single_choice' || taskItem.type === 'multiple_choice' ||
                taskItem.type === 'image_identification' ||
                taskItem.type === 'SINGLE_CHOICE' || taskItem.type === 'MULTIPLE_CHOICE' ||
                taskItem.type === 'IMAGE_IDENTIFICATION') && (
                (() => {
                  const optionsWithKeys = getQuestionOptionsWithKeys()

                  if (optionsWithKeys.length === 0) {
                    return (
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg mb-6">
                        <p className="text-yellow-800 dark:text-yellow-200 text-sm text-center">
                          No options available for this task.
                        </p>
                      </div>
                    )
                  }

                  return (
                    <div className="mb-4">
                      {/* Multiple Choice Instructions */}
                      {(taskItem.type === 'multiple_choice' || taskItem.type === 'MULTIPLE_CHOICE') && !isAnswered && (
                        <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                          <p className="text-sm text-blue-800 dark:text-blue-200 flex items-center gap-2">
                            <Check className="h-4 w-4" />
                            Select all correct answers ({selectedAnswers.length} selected)
                          </p>
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-2">
                        {optionsWithKeys.map((option, index) => {
                          const isMultipleChoice = taskItem.type === 'multiple_choice' || taskItem.type === 'MULTIPLE_CHOICE'
                          const isSelected = isMultipleChoice
                            ? selectedAnswers.includes(option.key)
                            : selectedAnswer === option.key
                          const isDisabled = submitting || isAnswered

                          // Check if this option was the user's answer
                          const isUserAnswer = isAnswered && (
                            isMultipleChoice
                              ? (taskItem.user_answer?.selected_options?.includes(option.key) ||
                                 (Array.isArray(taskItem.user_answer) && taskItem.user_answer.includes(option.key)))
                              : (taskItem.user_answer?.selected_option === option.key ||
                                 taskItem.user_answer === option.key)
                          )

                          // Check if this is the correct answer
                          const isCorrectAnswer = isAnswered && (
                            taskItem.correct_answer?.selected_option === option.key ||
                            taskItem.correct_answer === option.key ||
                            taskItem.answer === option.key
                          )

                          return (
                            <motion.button
                              key={option.key}
                              className={cn(
                                "w-full text-left p-3 border rounded-lg transition-colors text-sm font-medium min-h-[50px] flex items-center",
                                // Not answered yet - show selection state
                                !isAnswered && isSelected && "bg-purple-100 dark:bg-purple-900/70 border-purple-500 dark:border-purple-400 text-purple-800 dark:text-purple-200",
                                !isAnswered && !isSelected && "hover:bg-purple-50 dark:hover:bg-purple-900/40 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-500",
                                // Already answered - show result-based styling
                                isAnswered && isUserAnswer && isCorrect && "bg-green-100 dark:bg-green-900/70 border-green-500 dark:border-green-400 text-green-800 dark:text-green-200",
                                isAnswered && isUserAnswer && !isCorrect && "bg-red-100 dark:bg-red-900/70 border-red-500 dark:border-red-400 text-red-800 dark:text-red-200",
                                isAnswered && !isUserAnswer && isCorrectAnswer && "bg-green-100 dark:bg-green-900/70 border-green-500 dark:border-green-400 text-green-800 dark:text-green-200",
                                isAnswered && !isUserAnswer && !isCorrectAnswer && "bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 border-gray-300 dark:border-gray-600",
                                // Disabled state
                                isDisabled && "cursor-not-allowed"
                              )}
                              onClick={() => isMultipleChoice
                                ? handleMultipleChoiceSelect(option.key)
                                : handleSingleChoiceSelect(option.key)}
                              disabled={isDisabled}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              whileHover={!isDisabled ? { scale: 1.02 } : {}}
                              whileTap={!isDisabled ? { scale: 0.98 } : {}}
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="flex items-center gap-3 flex-1">
                                  {/* Visual indicator for multiple choice */}
                                  {isMultipleChoice && !isAnswered && (
                                    <div className={cn(
                                      "w-5 h-5 border-2 rounded flex items-center justify-center transition-all",
                                      isSelected
                                        ? "bg-purple-600 border-purple-600 text-white"
                                        : "border-gray-300 dark:border-gray-600"
                                    )}>
                                      {isSelected && <Check className="h-3 w-3" />}
                                    </div>
                                  )}

                                  {/* Single choice indicator */}
                                  {!isMultipleChoice && !isAnswered && (
                                    <div className={cn(
                                      "w-5 h-5 border-2 rounded-full flex items-center justify-center transition-all",
                                      isSelected
                                        ? "bg-purple-600 border-purple-600"
                                        : "border-gray-300 dark:border-gray-600"
                                    )}>
                                      {isSelected && <div className="w-2 h-2 bg-white rounded-full" />}
                                    </div>
                                  )}

                                  <span className="flex-1">{option.value}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                  {/* Hidden audio elements for each option */}
                                  {(() => {
                                    const optionMetadata = getOptionMetadata(option.key)
                                    if (optionMetadata?.audio_url) {
                                      return (
                                        <audio
                                          id={`option-audio-${option.key}`}
                                          src={optionMetadata.audio_url}
                                          onEnded={() => setPlayingAudio(null)}
                                          onPause={() => setPlayingAudio(null)}
                                          preload="none"
                                          style={{ display: 'none' }}
                                        />
                                      )
                                    }
                                    return null
                                  })()}

                                  {/* Show audio icon if option has audio */}
                                  {getOptionMetadata(option.key)?.audio_url && (
                                    <Volume2 className="h-4 w-4 text-blue-600" />
                                  )}

                                  {isAnswered && isCorrectAnswer && (
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                  )}
                                </div>
                              </div>
                            </motion.button>
                          )
                        })}
                      </div>
                    </div>
                  )
                })()
              )}

              {/* Text Input */}
              {(taskItem.type === 'text_input' || taskItem.type === 'TEXT_INPUT') && (
                <div className="mb-4">
                  <textarea
                    value={textAnswer}
                    onChange={(e) => setTextAnswer(e.target.value)}
                    disabled={isAnswered}
                    placeholder="Enter your answer..."
                    className="w-full p-3 border border-border rounded-lg resize-none h-24 bg-background text-foreground"
                  />
                  {isAnswered && taskItem.user_answer?.text_answer && (
                    <div className="mt-2 p-2 bg-muted rounded-lg">
                      <p className="text-xs text-muted-foreground mb-1">Your answer:</p>
                      <p className="text-sm text-foreground">{taskItem.user_answer.text_answer}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Audio Recording for Speak Word */}
              {(taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD') && (
                <div className="mb-4">
                  <div className="p-4 border border-border rounded-lg bg-muted/20">
                    <div className="flex items-center gap-2 mb-3 text-amber-600 dark:text-amber-400">
                      <Mic className="h-5 w-5" />
                      <h3 className="font-medium">Speak the Word</h3>
                    </div>
                    <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-100 dark:border-amber-800 rounded-lg p-3 mb-4">
                      <p className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">
                        {taskItem.question.answer_hint ? 'Speak this word:' : 'Speak clearly into the microphone'}
                      </p>
                      {taskItem.question.answer_hint && (
                        <p className="text-2xl font-bold text-amber-900 dark:text-amber-100">
                          {taskItem.question.answer_hint}
                        </p>
                      )}
                      {!taskItem.question.answer_hint && (
                        <p className="text-sm text-amber-700/80 dark:text-amber-300/80">
                          Click the microphone to start recording.
                        </p>
                      )}
                    </div>
                    
                    <div className="w-full">
                      <AudioRecorder
                        onRecordingComplete={(blob) => setAudioBlob(blob)}
                        disabled={isAnswered}
                        initialAudioUrl={taskItem.user_answer?.audio_url || null}
                        maxDuration={15}
                      />
                      <p className="text-xs text-muted-foreground mt-2 text-center">
                        {!audioBlob && !taskItem.user_answer?.audio_url 
                          ? 'Click the microphone to start recording' 
                          : 'Click the play button to listen to your recording'}
                      </p>
                    </div>
                    
                    {isAnswered && taskItem.user_answer?.audio_url && (
                      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                        <p className="text-xs text-muted-foreground mb-2">Your recording:</p>
                        <audio 
                          src={taskItem.user_answer.audio_url} 
                          controls 
                          className="w-full"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Submit Button & Navigation */}
              <div className="flex items-center justify-between gap-4">
                {!isAnswered ? (
                  <>
                    <div className="text-sm text-muted-foreground">
                      {taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD'
                        ? 'Record your voice before submitting'
                        : (taskItem.type === 'multiple_choice' || taskItem.type === 'MULTIPLE_CHOICE')
                          ? `${selectedAnswers.length} option${selectedAnswers.length !== 1 ? 's' : ''} selected`
                          : 'Review your answer before submitting'}
                    </div>
                    <button
                      onClick={handleSubmit}
                      disabled={submitting ||
                        (taskItem.type === 'speak_word' || taskItem.type === 'SPEAK_WORD'
                          ? !audioBlob && !taskItem.user_answer?.audio_url
                          : (taskItem.type === 'multiple_choice' || taskItem.type === 'MULTIPLE_CHOICE')
                            ? selectedAnswers.length === 0
                            : !selectedAnswer && !textAnswer)
                      }
                      className={cn(
                        "px-6 py-3 rounded-lg font-medium transition-all",
                        "bg-primary text-primary-foreground hover:bg-primary/90",
                        "disabled:opacity-50 disabled:cursor-not-allowed"
                      )}
                    >
                      {submitting ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Submitting...
                        </div>
                      ) : (
                        'Submit Answer'
                      )}
                    </button>
                  </>
                ) : (
                  <div className={cn(
                    "w-full p-4 rounded-lg border text-center",
                    isCorrect ? "border-green-500 bg-green-50 dark:bg-green-900/20" : "border-red-500 bg-red-50 dark:bg-red-900/20"
                  )}>
                    <div className="flex items-center justify-center gap-2 mb-2">
                      {isCorrect ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      <span className={cn(
                        "font-medium",
                        isCorrect ? "text-green-600" : "text-red-600"
                      )}>
                        {isCorrect ? 'Correct!' : 'Incorrect'}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      You scored {taskItem.scored || 0} out of {taskItem.total_score || 0} points
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center bg-card border border-border rounded-lg p-6 max-w-md"
            >
              <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-muted mb-3">
                <AlertCircle className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2 text-card-foreground">Task not found</h3>
              <p className="text-muted-foreground text-sm">
                The requested task could not be found.
              </p>
            </motion.div>
          </div>
        )}
      </div>

      {/* Full-screen image preview modal */}
      <AnimatePresence>
        {showImagePreview && (
          <motion.div
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowImagePreview(false)}
          >
            <motion.div
              className="relative max-w-full max-h-full"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={getMediaUrl()}
                alt="Task media - Full view"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
              <button
                className="absolute top-4 right-4 bg-black/60 text-white p-2 rounded-full hover:bg-black/80 transition-all"
                onClick={() => setShowImagePreview(false)}
              >
                <X className="h-6 w-6" />
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </MainLayout>
  )
}

export default TaskItemComponent
