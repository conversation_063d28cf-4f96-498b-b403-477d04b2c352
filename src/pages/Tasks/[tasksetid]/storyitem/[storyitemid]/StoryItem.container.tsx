import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../../../../store/hooks'
import { storyService, SingleStoryResponse } from '../../../../../services/story/storyService'
import { taskService } from '../../../../../services/task/taskService'
import { markStoryAsVisited, undismissStoryNotification } from '../../../../../store/slices/storySlice'
import StoryItemComponent from './StoryItem.component.tsx'
import NavigationTransition from '../../../../../components/ui/NavigationTransition'

interface StoryItemState {
  loading: boolean
  error: string | null
  storyData: SingleStoryResponse | null
  storyList: string[]
  currentStoryIndex: number
  loadingStoryList: boolean
  navigating: boolean
  navigationMessage: string
  isInitialLoad: boolean
}

/**
 * StoryItem Container - Handles logic and state for story item page within tasks structure
 */
const StoryItemContainer: React.FC = () => {
  const { tasksetid, storyitemid } = useParams<{ tasksetid: string; storyitemid: string }>()
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()

  const [state, setState] = useState<StoryItemState>({
    loading: false,
    error: null,
    storyData: null,
    storyList: [],
    currentStoryIndex: 0,
    loadingStoryList: false,
    navigating: false,
    navigationMessage: '',
    isInitialLoad: true
  })

  // Fetch story list from task set when component mounts
  useEffect(() => {
    const fetchStoryList = async () => {
      if (!tasksetid) return

      try {
        setState(prev => ({ ...prev, loadingStoryList: true, error: null }))

        // Get task set with story IDs
        const taskSetWithStories = await taskService.getTaskSetWithStoryIds(tasksetid)

        if (taskSetWithStories.stories && taskSetWithStories.stories.length > 0) {
          const currentIndex = taskSetWithStories.stories.findIndex(id => id === storyitemid)
          setState(prev => ({
            ...prev,
            storyList: taskSetWithStories.stories || [],
            currentStoryIndex: currentIndex >= 0 ? currentIndex : 0,
            loadingStoryList: false
          }))
        } else {
          setState(prev => ({
            ...prev,
            error: 'No stories found in this task set',
            loadingStoryList: false
          }))
        }
      } catch (error) {
        console.error('Failed to fetch story list:', error)
        setState(prev => ({
          ...prev,
          error: 'Failed to load story list',
          loadingStoryList: false
        }))
      }
    }

    fetchStoryList()
  }, [tasksetid, storyitemid])

  // Fetch current story data when story ID changes
  useEffect(() => {
    const fetchStoryData = async () => {
      if (!storyitemid) return

      try {
        // Only show loading on initial load, not during navigation
        if (state.isInitialLoad) {
          setState(prev => ({ ...prev, loading: true, error: null }))
        }

        // Fetch story data using the new API format
        const response = await storyService.getSingleStory(storyitemid)

        setState(prev => ({
          ...prev,
          storyData: response,
          loading: false,
          isInitialLoad: false
        }))

        // Mark story as visited
        dispatch(markStoryAsVisited(storyitemid))
        dispatch(undismissStoryNotification(storyitemid))

      } catch (error) {
        console.error('Failed to fetch story data:', error)
        setState(prev => ({
          ...prev,
          error: 'Failed to load story',
          loading: false,
          isInitialLoad: false
        }))
      }
    }

    fetchStoryData()
  }, [storyitemid, dispatch, state.isInitialLoad])

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  const handlePreviousStory = () => {
    if (state.currentStoryIndex > 0 && state.storyList.length > 0) {
      const prevStoryId = state.storyList[state.currentStoryIndex - 1]
      navigate(`/tasks/${tasksetid}/storyitem/${prevStoryId}`)
    }
  }

  const handleNextStory = () => {
    if (state.currentStoryIndex < state.storyList.length - 1 && state.storyList.length > 0) {
      const nextStoryId = state.storyList[state.currentStoryIndex + 1]
      navigate(`/tasks/${tasksetid}/storyitem/${nextStoryId}`)
    }
  }

  const handleGoBack = () => {
    // Navigate back to the task set detail page
    if (tasksetid) {
      navigate(`/tasks/${tasksetid}`)
    } else {
      navigate('/tasks')
    }
  }

  const handleViewQuiz = async () => {
    // Navigate to the first task in the task set
    if (tasksetid) {
      try {
        // Get task set to find the first task
        const taskSetWithTasks = await taskService.getTaskSet(tasksetid)
        if (taskSetWithTasks.tasks && taskSetWithTasks.tasks.length > 0) {
          const firstTaskId = taskSetWithTasks.tasks[0]
          navigate(`/tasks/${tasksetid}/taskitem/${firstTaskId}`)
        } else {
          // No tasks available, go to task set page
          navigate(`/tasks/${tasksetid}`)
        }
      } catch (error) {
        console.error('Failed to fetch task set:', error)
        // Fallback to task set page
        navigate(`/tasks/${tasksetid}`)
      }
    }
  }

  if (!isAuthenticated || !user) {
    return null
  }

  if (!tasksetid || !storyitemid) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Invalid Story</h2>
          <p className="text-muted-foreground">Missing task set ID or story ID.</p>
        </div>
      </div>
    )
  }

  return (
    <StoryItemComponent
      taskSetId={tasksetid}
      storyId={storyitemid}
      storyData={state.storyData}
      currentStoryIndex={state.currentStoryIndex}
      totalStories={state.storyList.length}
      isLoading={state.loading && state.isInitialLoad}
      error={state.error}
      onPreviousStory={handlePreviousStory}
      onNextStory={handleNextStory}
      onGoBack={handleGoBack}
      onViewQuiz={handleViewQuiz}
      canGoPrevious={state.currentStoryIndex > 0}
      canGoNext={state.currentStoryIndex < state.storyList.length - 1}
    />
  )
}

export default StoryItemContainer
