import React, { useEffect, useState } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../../store/hooks'
import { taskService } from '../../../services/task/taskService'
import { useNavigation } from '../../../contexts/NavigationContext'
import TaskSetDetailComponent from './TaskSetDetail.component'
import Al<PERSON> from '../../../components/ui/Alert'
import {
  selectTaskSet,
  selectTaskSetScore,
  selectIsTaskSetCached,
  selectIsTaskSetScoreCached,
  selectIsCacheValid,
  setCurrentTaskSetId,
  setTaskSet,
  setTaskSetScore,
  setLoadingTaskSet,
  setLoadingTaskSetScore,
  setError,
  clearError,
  clearTaskSetCache
} from '../../../store/slices/taskSlice'



/**
 * TaskSetDetail Container - Handles logic and state for task set detail page
 */
const TaskSetDetailContainer: React.FC = () => {
  const { tasksetid } = useParams<{ tasksetid: string }>()
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { setBreadcrumbs, setCurrentTaskSet } = useNavigation()

  // Get data from Redux store
  const taskSet = useAppSelector((state) => tasksetid ? selectTaskSet(state, tasksetid) : null)
  const taskSetScore = useAppSelector((state) => tasksetid ? selectTaskSetScore(state, tasksetid) : null)
  const isTaskSetCached = useAppSelector((state) => tasksetid ? selectIsTaskSetCached(state, tasksetid) : false)
  const isTaskSetScoreCached = useAppSelector((state) => tasksetid ? selectIsTaskSetScoreCached(state, tasksetid) : false)
  const isCacheValid = useAppSelector((state) => tasksetid ? selectIsCacheValid(state, `taskset_${tasksetid}`) : false)
  const isTaskSetScoreCacheValid = useAppSelector((state) => tasksetid ? selectIsCacheValid(state, `score_${tasksetid}`) : false)
  const loading = useAppSelector((state) => state.task.loadingTaskSet)
  const loadingScore = useAppSelector((state) => tasksetid ? state.task.loadingTaskSetScore[tasksetid] || false : false)
  const error = useAppSelector((state) => state.task.error)

  // Local state for task questions
  const [taskQuestions, setTaskQuestions] = useState<any[]>([])
  const [loadingQuestions, setLoadingQuestions] = useState<boolean>(false)

  // Local state for alert
  const [alert, setAlert] = useState<{
    open: boolean
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    description?: string
  }>({
    open: false,
    type: 'info',
    title: '',
    description: ''
  })



  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Fetch task set and task items based on URL with caching
  useEffect(() => {
    const fetchTaskSetData = async () => {
      if (!tasksetid || !user) return

      // Clear any previous errors
      dispatch(clearError())

      // Check if we have valid cached data
      if (isTaskSetCached && isCacheValid) {
        console.log('Using cached task set data')
        // No longer prefetch task questions - use lazy loading
        return
      }

      try {
        dispatch(setLoadingTaskSet(true))

        // Fetch task set details
        const fetchedTaskSet = await taskService.getTaskSet(tasksetid, false, false, {
          onError: (error: any) => {
            dispatch(setError(error.message))
          }
        })

        // Cache the task set in Redux
        dispatch(setTaskSet({ id: tasksetid, taskSet: fetchedTaskSet }))
        dispatch(setCurrentTaskSetId(tasksetid))

        // Fetch task set score if not cached or invalid
        if (!isTaskSetScoreCached || !isTaskSetScoreCacheValid) {
          dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: true }))
          try {
            const scoreData = await taskService.getTaskSetScore(tasksetid, {
              onError: (error) => {
                console.error('Error fetching task set score:', error)
              }
            })
            dispatch(setTaskSetScore({ id: tasksetid, score: scoreData }))
          } catch (scoreError) {
            console.error('Failed to fetch task set score:', scoreError)
          } finally {
            dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: false }))
          }
        }

        // No longer prefetch task questions - use lazy loading instead

        // Update navigation context
        setCurrentTaskSet({ id: tasksetid, name: getTaskSetDisplayName(fetchedTaskSet) })
        setBreadcrumbs([
          {
            id: tasksetid,
            name: getTaskSetDisplayName(fetchedTaskSet),
            path: `/tasks/${tasksetid}`,
            type: 'taskset',
            isActive: true
          }
        ])

      } catch (err) {
        console.error('Error fetching task set data:', err)
        dispatch(setError('Failed to load task set. Please try again.'))
      } finally {
        dispatch(setLoadingTaskSet(false))
      }
    }

    if (isAuthenticated && user && tasksetid) {
      fetchTaskSetData()
    }
  }, [isAuthenticated, user, tasksetid, isTaskSetCached, isTaskSetScoreCached, isCacheValid, isTaskSetScoreCacheValid])

  // Helper function to get display name from input_content
  const getTaskSetDisplayName = (taskSet: any) => {
    if (!taskSet?.input_content) return 'Task Set'

    // If input_content is a string, return it
    if (typeof taskSet.input_content === 'string') {
      return taskSet.input_content
    }

    // If input_content is an object, extract meaningful name
    if (typeof taskSet.input_content === 'object') {
      return taskSet.input_content.file_name ||
             taskSet.input_content.object_name ||
             'Audio Task Set'
    }

    return 'Task Set'
  }

  // Fetch task questions for display in cards
  const fetchTaskQuestions = async () => {
    if (!taskSet?.tasks || taskSet.tasks.length === 0) return

    setLoadingQuestions(true)
    try {
      const questionPromises = taskSet.tasks.map((taskId: string) =>
        taskService.getTaskQuestion(taskId).catch(error => {
          console.error(`Failed to fetch question for task ${taskId}:`, error)
          return null
        })
      )

      const questions = await Promise.all(questionPromises)
      const validQuestions = questions.filter(q => q !== null).map((q, index) => ({
        ...q,
        id: taskSet.tasks[index],
        _id: taskSet.tasks[index]
      }))

      setTaskQuestions(validQuestions)
    } catch (error) {
      console.error('Error fetching task questions:', error)
    } finally {
      setLoadingQuestions(false)
    }
  }

  // Fetch questions when taskSet is loaded
  useEffect(() => {
    if (taskSet?.tasks && taskSet.tasks.length > 0) {
      fetchTaskQuestions()
    }
  }, [taskSet?.tasks])

  // Navigate to task item when card is clicked
  const handleTaskItemClick = (taskItemId: string, taskIndex: number) => {
    if (tasksetid && taskItemId) {
      navigate(`/tasks/${tasksetid}/taskitem/${taskItemId}`)
    }
  }

  // Navigate to first task when View Tasks is clicked
  const handleViewTasks = async () => {
    if (tasksetid && taskSet?.tasks && taskSet.tasks.length > 0) {
      const firstTaskId = taskSet.tasks[0]
      navigate(`/tasks/${tasksetid}/taskitem/${firstTaskId}`)
    }
  }

  // Navigate to first story when View Stories is clicked
  const handleViewStories = async () => {
    if (tasksetid) {
      try {
        // Get task set with story IDs
        const taskSetWithStories = await taskService.getTaskSetWithStoryIds(tasksetid)

        if (taskSetWithStories.stories && taskSetWithStories.stories.length > 0) {
          // Navigate to the first story using the correct route
          const firstStoryId = taskSetWithStories.stories[0]
          navigate(`/tasks/${tasksetid}/storyitem/${firstStoryId}`)
        } else {
          // No stories available, show attractive alert popup
          setAlert({
            open: true,
            type: 'info',
            title: 'No Stories Available',
            description: 'There are no stories to display for this task set at the moment. Stories may be added later.'
          })
        }
      } catch (error) {
        console.error('Failed to fetch stories:', error)
        setAlert({
          open: true,
          type: 'error',
          title: 'Failed to Load Stories',
          description: 'Unable to load stories at this time. Please check your connection and try again.'
        })
      }
    }
  }



  // Reset task - call retry endpoint and clear cache
  const handleResetTask = async () => {
    if (!tasksetid) return

    try {
      // Call the retry endpoint
      await taskService.retryTaskSet(tasksetid)

      // Clear cache and refresh
      dispatch(clearTaskSetCache(tasksetid))
      handleRefresh()
    } catch (error) {
      console.error('Failed to retry task set:', error)
      dispatch(setError('Failed to retry task set. Please try again.'))
    }
  }



  // Refresh data
  const handleRefresh = async () => {
    if (!tasksetid || !user) return

    try {
      dispatch(setLoadingTaskSet(true))
      dispatch(clearError())

      const fetchedTaskSet = await taskService.getTaskSet(tasksetid, false, false)

      // Update cache
      dispatch(setTaskSet({ id: tasksetid, taskSet: fetchedTaskSet }))

      // Also refresh the task set score
      dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: true }))
      try {
        const scoreData = await taskService.getTaskSetScore(tasksetid)
        dispatch(setTaskSetScore({ id: tasksetid, score: scoreData }))
      } catch (scoreError) {
        console.error('Failed to refresh task set score:', scoreError)
      } finally {
        dispatch(setLoadingTaskSetScore({ id: tasksetid, loading: false }))
      }

      // No longer prefetch task questions during refresh - use lazy loading

    } catch (err) {
      console.error('Error refreshing task set data:', err)
      dispatch(setError('Failed to refresh data. Please try again.'))
    } finally {
      dispatch(setLoadingTaskSet(false))
    }
  }

  if (!isAuthenticated || !user) {
    return null
  }

  if (!tasksetid) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Invalid Task Set</h2>
          <p className="text-muted-foreground">No task set ID provided.</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <TaskSetDetailComponent
        taskSetId={tasksetid}
        taskSet={taskSet}
        taskSetScore={taskSetScore}
        taskQuestions={taskQuestions}
        loading={loading}
        loadingScore={loadingScore}
        loadingQuestions={loadingQuestions}
        error={error}
        onTaskItemClick={handleTaskItemClick}
        onViewTasks={handleViewTasks}
        onViewStories={handleViewStories}
        onResetTask={handleResetTask}
        onRefresh={handleRefresh}
      />

      {/* Alert for stories not available */}
      <Alert
        open={alert.open}
        onOpenChange={(open) => setAlert(prev => ({ ...prev, open }))}
        title={alert.title}
        description={alert.description}
        type={alert.type}
      />
    </>
  )
}

export default TaskSetDetailContainer
