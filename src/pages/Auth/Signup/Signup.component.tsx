import React from 'react'
import { motion } from 'framer-motion'
import { SignupComponentProps } from '../types'
import EmailInput from '../shared/EmailInput'
import PasswordInput from '../shared/PasswordInput'
import TextInput from '../shared/TextInput'
import SubmitButton from '../shared/SubmitButton'

/**
 * Signup Component - Pure UI component for signup form
 */
const SignupComponent: React.FC<SignupComponentProps> = ({
  formData,
  errors,
  isLoading,
  onInputChange,
  onSubmit,
  onClearError,
}) => {
  // Form validation for submit button
  const isFormValid = () => {
    // Safety check to ensure formData has all required fields
    if (!formData.username || !formData.email || !formData.password || !formData.confirmPassword) {
      return false
    }

    return (
      formData.username.trim() &&
      formData.email.trim() &&
      formData.password.trim() &&
      formData.confirmPassword.trim() &&
      formData.password === formData.confirmPassword
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <motion.h1
          className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
        >
          Create Account
        </motion.h1>
        <motion.p
          className="text-gray-600 dark:text-gray-400"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          Join us and start your learning journey
        </motion.p>
      </motion.div>

      {/* General Error Display */}
      {errors.general && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center justify-between">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
            <button
              onClick={onClearError}
              className="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <form onSubmit={onSubmit} className="space-y-4">
        {/* Username field */}
        <TextInput
          label="Username"
          value={formData.username || ''}
          error={errors.username}
          onChange={(value) => onInputChange('username', value)}
          disabled={isLoading}
          placeholder="Choose a username"
          required
        />

        {/* Email field */}
        <EmailInput
          value={formData.email || ''}
          error={errors.email}
          onChange={(value) => onInputChange('email', value)}
          disabled={isLoading}
        />

        {/* Full name field */}
        <TextInput
          label="Full Name"
          value={formData.full_name || ''}
          error={errors.full_name}
          onChange={(value) => onInputChange('full_name', value)}
          disabled={isLoading}
          placeholder="Enter your full name (optional)"
        />

        {/* Password field */}
        <PasswordInput
          value={formData.password || ''}
          error={errors.password}
          onChange={(value) => onInputChange('password', value)}
          disabled={isLoading}
          placeholder="Create a password"
        />

        {/* Confirm password field */}
        <PasswordInput
          value={formData.confirmPassword || ''}
          error={errors.confirmPassword}
          onChange={(value) => onInputChange('confirmPassword', value)}
          disabled={isLoading}
          placeholder="Confirm your password"
        />

        {/* Submit button */}
        <SubmitButton
          mode="signup"
          isLoading={isLoading}
          disabled={isLoading || !isFormValid()}
        />
      </form>
    </motion.div>
  )
}

export default SignupComponent
