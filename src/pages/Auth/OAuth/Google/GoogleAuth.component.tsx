import React from 'react'
import { GoogleLogin } from '@react-oauth/google'
import { motion } from 'framer-motion'
import { GoogleAuthComponentProps } from '../../types'

/**
 * GoogleAuth Component - Google OAuth sign-in button component
 */
const GoogleAuthComponent: React.FC<GoogleAuthComponentProps> = ({
  onSuccess,
  onError,
  disabled = false,
}) => {
  const handleSuccess = (credentialResponse: any) => {
    if (credentialResponse.credential) {
      onSuccess(credentialResponse.credential)
    } else {
      onError()
    }
  }

  const handleError = () => {
    console.error('Google Sign-In failed')
    onError()
  }

  return (
    <div className="w-full max-w-full">
      {/* Custom styled wrapper for Google button */}
      <motion.div
        whileHover={{
          scale: 1.02,
          y: -2,
          boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
        }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
        className="relative overflow-hidden rounded-2xl transition-all duration-300 w-full"
      >
        {/* Default background with border and effects */}
        <div className="relative border border-border rounded-2xl overflow-hidden shadow-sm w-full max-w-full">
          <div className="google-login-wrapper w-full max-w-full">
            <GoogleLogin
              onSuccess={handleSuccess}
              onError={handleError}
              theme="outline"
              size="large"
              width="100%"
              text="signin_with"
              shape="rectangular"
              disabled={disabled}
            />
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default GoogleAuthComponent
