import React from 'react'
import { ModeSwitchProps } from '../types'

/**
 * ModeSwitch - Component to switch between login and signup modes
 */
const ModeSwitch: React.FC<ModeSwitchProps> = ({
  mode,
  onModeChange,
  disabled = false,
}) => {
  return (
    <div className="text-center">
      <p className="text-sm text-gray-600 dark:text-gray-400">
        {mode === 'login' ? "Don't have an account?" : "Already have an account?"}
        {' '}
        <button
          type="button"
          onClick={() => onModeChange(mode === 'login' ? 'signup' : 'login')}
          disabled={disabled}
          className="font-medium text-purple-600 hover:text-purple-500 dark:text-purple-400 dark:hover:text-purple-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {mode === 'login' ? 'Sign up' : 'Sign in'}
        </button>
      </p>
    </div>
  )
}

export default ModeSwitch
