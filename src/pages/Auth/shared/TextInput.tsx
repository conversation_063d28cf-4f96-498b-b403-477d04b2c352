import React from 'react'
import { TextInputProps } from '../types'

/**
 * TextInput - Reusable text input component with validation styling
 */
const TextInput: React.FC<TextInputProps> = ({
  value,
  error,
  onChange,
  disabled = false,
  placeholder,
  label,
  type = 'text',
  required = false,
}) => {
  const inputId = label.toLowerCase().replace(/\s+/g, '-')

  return (
    <div>
      <label 
        htmlFor={inputId} 
        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <input
        id={inputId}
        type={type}
        autoComplete={type === 'email' ? 'email' : type === 'password' ? 'current-password' : 'off'}
        required={required}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        placeholder={placeholder}
        className={`
          w-full px-4 py-3 border rounded-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700
          placeholder-gray-500 dark:placeholder-gray-400
          focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
          disabled:opacity-50 disabled:cursor-not-allowed
          transition-colors duration-200
          ${error 
            ? 'border-red-500 focus:ring-red-500' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
        `}
      />
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  )
}

export default TextInput
