import React from 'react'

interface EmailOrUsernameInputProps {
  value: string
  error?: string
  onChange: (value: string) => void
  disabled?: boolean
}

/**
 * EmailOrUsernameInput - Input that accepts either email or username
 */
const EmailOrUsernameInput: React.FC<EmailOrUsernameInputProps> = ({
  value,
  error,
  onChange,
  disabled = false,
}) => {
  return (
    <div>
      <label htmlFor="emailOrUsername" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Email or Username
      </label>
      <input
        id="emailOrUsername"
        type="text"
        autoComplete="username"
        required
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={`
          w-full px-4 py-3 border rounded-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700
          placeholder-gray-500 dark:placeholder-gray-400
          focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
          disabled:opacity-50 disabled:cursor-not-allowed
          transition-colors duration-200
          ${error 
            ? 'border-red-500 focus:ring-red-500' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
        `}
        placeholder="Enter your email or username"
      />
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  )
}

export default EmailOrUsernameInput
