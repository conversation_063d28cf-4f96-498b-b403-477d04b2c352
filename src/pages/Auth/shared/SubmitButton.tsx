import React from 'react'
import { motion } from 'framer-motion'
import { SubmitButtonProps } from '../types'

/**
 * Enhanced SubmitButton - Modern submit button with animations and loading state
 */
const SubmitButton: React.FC<SubmitButtonProps> = ({
  isLoading,
  disabled = false,
  mode,
}) => {
  return (
    <motion.button
      type="submit"
      disabled={disabled}
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 25 }}
      className={`
        w-full flex justify-center items-center px-6 py-4 border border-transparent rounded-xl
        text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-purple-600
        hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500
        disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:from-blue-600 disabled:hover:to-purple-600
        transition-all duration-300 relative overflow-hidden
        ${isLoading ? 'cursor-wait' : ''}
      `}
    >
      {/* Shimmer effect */}
      {!disabled && !isLoading && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
          animate={{
            x: ['-100%', '100%']
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}

      {isLoading ? (
        <>
          <motion.svg
            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </motion.svg>
          <motion.span
            animate={{ opacity: [1, 0.7, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            {mode === 'signup' ? 'Creating account...' : 'Signing in...'}
          </motion.span>
        </>
      ) : (
        <span className="relative z-10">
          {mode === 'signup' ? 'Create Account' : 'Sign In'}
        </span>
      )}
    </motion.button>
  )
}

export default SubmitButton
