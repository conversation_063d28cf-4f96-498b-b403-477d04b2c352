export type AuthMode = 'login' | 'signup'

export interface LoginFormData {
  emailOrUsername: string  // Can be either email or username
  password: string
}

export interface SignupFormData {
  username: string
  email: string
  password: string
  confirmPassword: string
  full_name?: string
}

export type AuthFormData = LoginFormData | SignupFormData

export interface AuthFormErrors {
  emailOrUsername?: string  // For login
  email?: string           // For signup
  password?: string
  confirmPassword?: string
  username?: string
  full_name?: string
  general?: string
}

export interface AuthContainerProps {
  mode?: AuthMode
}

export interface AuthComponentProps {
  mode: AuthMode
  formData: AuthFormData
  errors: AuthFormErrors
  isLoading: boolean
  onModeChange: (mode: AuthMode) => void
  onInputChange: (field: string, value: string) => void
  onSubmit: (e: React.FormEvent) => void
  onGoogleAuth: (credential: string) => void
  onClearError: () => void
}

// Component-specific props
export interface LoginComponentProps {
  formData: LoginFormData
  errors: AuthFormErrors
  isLoading: boolean
  onInputChange: (field: string, value: string) => void
  onSubmit: (e: React.FormEvent) => void
  onClearError: () => void
}

export interface SignupComponentProps {
  formData: SignupFormData
  errors: AuthFormErrors
  isLoading: boolean
  onInputChange: (field: string, value: string) => void
  onSubmit: (e: React.FormEvent) => void
  onClearError: () => void
}

export interface GoogleAuthComponentProps {
  onSuccess: (credential: string) => void
  onError: () => void
  disabled?: boolean
}

// Shared component props
export interface EmailInputProps {
  value: string
  error?: string
  onChange: (value: string) => void
  disabled?: boolean
}

export interface PasswordInputProps {
  value: string
  error?: string
  onChange: (value: string) => void
  disabled?: boolean
  placeholder?: string
}

export interface TextInputProps {
  value: string
  error?: string
  onChange: (value: string) => void
  disabled?: boolean
  placeholder?: string
  label: string
  type?: string
  required?: boolean
}

export interface SubmitButtonProps {
  isLoading: boolean
  disabled?: boolean
  mode: AuthMode
}

export interface ModeSwitchProps {
  mode: AuthMode
  onModeChange: (mode: AuthMode) => void
  disabled?: boolean
}
