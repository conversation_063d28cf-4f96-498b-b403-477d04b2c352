import React from 'react'
import { motion } from 'framer-motion'
import { LoginComponentProps } from '../types'
import EmailOrUsernameInput from '../shared/EmailOrUsernameInput'
import PasswordInput from '../shared/PasswordInput'
import SubmitButton from '../shared/SubmitButton'

/**
 * Login Component - Pure UI component for login form
 */
const LoginComponent: React.FC<LoginComponentProps> = ({
  formData,
  errors,
  isLoading,
  onInputChange,
  onSubmit,
  onClearError,
}) => {
  // Form validation for submit button
  const isFormValid = () => {
    return formData.emailOrUsername?.trim() && formData.password?.trim()
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <motion.h1
          className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
        >
          Welcome Back
        </motion.h1>
        <motion.p
          className="text-gray-600 dark:text-gray-400"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          Sign in to your account to continue learning
        </motion.p>
      </motion.div>

      {/* General Error Display */}
      {errors.general && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center justify-between">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
            <button
              onClick={onClearError}
              className="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <motion.form
        onSubmit={onSubmit}
        className="space-y-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        {/* Email or Username field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <EmailOrUsernameInput
            value={formData.emailOrUsername || ''}
            error={errors.emailOrUsername}
            onChange={(value) => onInputChange('emailOrUsername', value)}
            disabled={isLoading}
          />
        </motion.div>

        {/* Password field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <PasswordInput
            value={formData.password || ''}
            error={errors.password}
            onChange={(value) => onInputChange('password', value)}
            disabled={isLoading}
            placeholder="Enter your password"
          />
        </motion.div>

        {/* Submit button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <SubmitButton
            mode="login"
            isLoading={isLoading}
            disabled={isLoading || !isFormValid()}
          />
        </motion.div>
      </motion.form>
    </motion.div>
  )
}

export default LoginComponent
