import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppSelector } from '../../store/hooks'
import { useLearningStats, useLeaderboard } from '../../hooks/useLearningStats'
import DashboardComponent from './Dashboard.component'

/**
 * Dashboard Container - Handles logic and state
 */
const DashboardContainer: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.auth)
  const navigate = useNavigate()
  const { stats, loading: statsLoading, error: statsError, refetch } = useLearningStats()
  const {
    leaderboard,
    loading: leaderboardLoading,
    error: leaderboardError,
    refetch: refetchLeaderboard
  } = useLeaderboard({ skip: 0, limit: 10 })

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, navigate])

  // Redirect if onboarding not completed
  useEffect(() => {
    if (user && user.onboarding_completed === false) {
      navigate('/onboarding', { replace: true })
    }
  }, [user, navigate])

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <DashboardComponent
      user={user}
      stats={stats}
      statsLoading={statsLoading}
      statsError={statsError}
      onRefreshStats={refetch}
      leaderboard={leaderboard}
      leaderboardLoading={leaderboardLoading}
      leaderboardError={leaderboardError}
      onRefreshLeaderboard={refetchLeaderboard}
    />
  )
}

export default DashboardContainer
