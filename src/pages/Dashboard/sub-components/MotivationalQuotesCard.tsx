import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Quote, <PERSON>fresh<PERSON><PERSON>, <PERSON>, Star, Lightbulb } from 'lucide-react'
import { cn } from '../../../utils/cn'

interface MotivationalQuotesCardProps {
  cardVariants: any
}

interface Quote {
  text: string
  author: string
  category: 'learning' | 'motivation' | 'success' | 'wisdom'
}

const quotes: Quote[] = [
  {
    text: "The beautiful thing about learning is that no one can take it away from you.",
    author: "B.B. King",
    category: "learning"
  },
  {
    text: "Education is the most powerful weapon which you can use to change the world.",
    author: "<PERSON>",
    category: "learning"
  },
  {
    text: "The capacity to learn is a gift; the ability to learn is a skill; the willingness to learn is a choice.",
    author: "<PERSON>",
    category: "learning"
  },
  {
    text: "Learning never exhausts the mind.",
    author: "Leonardo da Vinci",
    category: "learning"
  },
  {
    text: "The more that you read, the more things you will know. The more that you learn, the more places you'll go.",
    author: "<PERSON><PERSON>",
    category: "learning"
  },
  {
    text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
    author: "<PERSON>",
    category: "motivation"
  },
  {
    text: "The only way to do great work is to love what you do.",
    author: "<PERSON>s",
    category: "success"
  },
  {
    text: "Believe you can and you're halfway there.",
    author: "<PERSON> <PERSON>",
    category: "motivation"
  },
  {
    text: "The expert in anything was once a beginner.",
    author: "<PERSON> <PERSON>",
    category: "wisdom"
  },
  {
    text: "Every accomplishment starts with the decision to try.",
    author: "<PERSON> F. <PERSON>",
    category: "motivation"
  }
]

const categoryIcons = {
  learning: Lightbulb,
  motivation: Heart,
  success: Star,
  wisdom: Quote
}

const categoryColors = {
  learning: 'from-blue-500 to-cyan-500',
  motivation: 'from-pink-500 to-rose-500',
  success: 'from-amber-500 to-orange-500',
  wisdom: 'from-purple-500 to-indigo-500'
}

const MotivationalQuotesCard: React.FC<MotivationalQuotesCardProps> = ({
  cardVariants
}) => {
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)

  const currentQuote = quotes[currentQuoteIndex]
  const IconComponent = categoryIcons[currentQuote.category]

  // Auto-rotate quotes every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuoteIndex((prev) => (prev + 1) % quotes.length)
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const handleRefresh = () => {
    setIsRefreshing(true)
    setTimeout(() => {
      setCurrentQuoteIndex((prev) => (prev + 1) % quotes.length)
      setIsRefreshing(false)
    }, 500)
  }

  return (
    <motion.div
      variants={cardVariants}
      whileHover={{ 
        scale: 1.02,
        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden shadow-lg h-full flex flex-col"
    >
      {/* Header */}
      <div className="p-4 border-b border-slate-200 dark:border-slate-700 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={cn(
            "p-2 rounded-lg shadow-sm bg-gradient-to-r",
            categoryColors[currentQuote.category]
          )}>
            <Quote className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Daily Inspiration
            </h3>
            <p className="text-xs text-slate-500 dark:text-slate-400 capitalize">
              {currentQuote.category} • Quote {currentQuoteIndex + 1} of {quotes.length}
            </p>
          </div>
        </div>
        <button
          onClick={handleRefresh}
          disabled={isRefreshing}
          className={cn(
            "p-2 rounded-full bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors",
            isRefreshing && "animate-spin"
          )}
        >
          <RefreshCw className="h-4 w-4 text-slate-600 dark:text-slate-400" />
        </button>
      </div>

      {/* Quote Content */}
      <div className="flex-1 p-6 flex flex-col justify-center">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuoteIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="text-center space-y-6"
          >
            {/* Quote Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className={cn(
                "w-16 h-16 mx-auto rounded-full flex items-center justify-center bg-gradient-to-r shadow-lg",
                categoryColors[currentQuote.category]
              )}
            >
              <IconComponent className="h-8 w-8 text-white" />
            </motion.div>

            {/* Quote Text */}
            <motion.blockquote
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="text-lg md:text-xl font-medium text-slate-700 dark:text-slate-300 leading-relaxed italic"
            >
              "{currentQuote.text}"
            </motion.blockquote>

            {/* Author */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="flex items-center justify-center gap-2"
            >
              <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-slate-300 dark:to-slate-600"></div>
              <cite className="text-sm font-semibold text-slate-600 dark:text-slate-400 not-italic">
                {currentQuote.author}
              </cite>
              <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-slate-300 dark:to-slate-600"></div>
            </motion.div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Progress Indicator */}
      <div className="p-4 border-t border-slate-200 dark:border-slate-700">
        <div className="flex gap-1">
          {quotes.map((_, index) => (
            <motion.div
              key={index}
              className={cn(
                "h-1 rounded-full transition-all duration-300",
                index === currentQuoteIndex
                  ? `bg-gradient-to-r ${categoryColors[currentQuote.category]}`
                  : "bg-slate-200 dark:bg-slate-700"
              )}
              style={{ flex: 1 }}
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ delay: index * 0.1 }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  )
}

export default MotivationalQuotesCard
