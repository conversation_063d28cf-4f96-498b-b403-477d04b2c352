import React from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON>fresh<PERSON><PERSON>,
  Target,
  BookOpen,
  Loader2,
  Brain,
  CheckCircle,
  Star
} from 'lucide-react'
import { cn } from '../../../utils/cn'
import { LearningStats } from '../../../services/stats/statsService'

interface LearningStatsCardProps {
  stats: LearningStats | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

const LearningStatsCard: React.FC<LearningStatsCardProps> = ({
  stats,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {


  return (
    <motion.div
      variants={cardVariants}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden shadow-lg"
    >
      {/* Enhanced Header */}
      <div className="flex items-center justify-between p-3 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-sm">
            <Brain className="h-4 w-4 text-white" />
          </div>
          <h3 className="text-base font-semibold text-slate-900 dark:text-white">
            Learning Stats
          </h3>
        </div>
        <button
          onClick={onRefresh}
          disabled={loading}
          className={cn(
            "p-1.5 rounded-lg transition-all duration-200",
            "bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700",
            "focus:outline-none focus:ring-2 focus:ring-blue-500",
            loading && "opacity-50 cursor-not-allowed"
          )}
        >
          <RefreshCw className={cn("h-3.5 w-3.5 text-slate-600 dark:text-slate-400", loading && "animate-spin")} />
        </button>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-3 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <p className="text-red-600 dark:text-red-400 text-xs font-medium">{error}</p>
        </motion.div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-2" />
            <p className="text-slate-600 dark:text-slate-400 text-sm">Loading...</p>
          </div>
        </div>
      ) : (
        <div className="p-4">
          {/* 2x2 Key Metrics Grid */}
          <div className="grid grid-cols-2 gap-4">
            {/* Learning Sessions */}
            <motion.div
              className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4 shadow-sm h-20 flex flex-col justify-center cursor-pointer"
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-purple-500 rounded">
                  <BookOpen className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">
                  Sessions
                </div>
              </div>
              <div className="text-xl font-bold text-purple-900 dark:text-purple-100">
                {stats?.total_sets || 0}
              </div>
            </motion.div>

            {/* Tasks Completed */}
            <motion.div
              className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-700 rounded-lg p-4 shadow-sm h-20 flex flex-col justify-center cursor-pointer"
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-green-500 rounded">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-green-700 dark:text-green-300 font-medium">
                  Completed
                </div>
              </div>
              <div className="text-xl font-bold text-green-900 dark:text-green-100">
                {stats?.total_attempted_tasks || 0}
              </div>
            </motion.div>

            {/* Accuracy Rate */}
            <motion.div
              className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 shadow-sm h-20 flex flex-col justify-center cursor-pointer"
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-blue-500 rounded">
                  <Target className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                  Accuracy
                </div>
              </div>
              <div className="text-xl font-bold text-blue-900 dark:text-blue-100">
                {stats?.accuracy_rate || 0}%
              </div>
            </motion.div>

            {/* Total Score */}
            <motion.div
              className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 border border-amber-200 dark:border-amber-700 rounded-lg p-4 shadow-sm h-20 flex flex-col justify-center cursor-pointer"
              whileHover={{ scale: 1.05, y: -2 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <div className="flex items-center gap-2 mb-2">
                <div className="p-1.5 bg-amber-500 rounded">
                  <Star className="h-4 w-4 text-white" />
                </div>
                <div className="text-sm text-amber-700 dark:text-amber-300 font-medium">
                  Points
                </div>
              </div>
              <div className="text-xl font-bold text-amber-900 dark:text-amber-100">
                {stats?.total_scored || 0}
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </motion.div>
  )
}

export default LearningStatsCard
