import React, { useMemo } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, Target, Database } from 'lucide-react'
import { ResponsiveSunburst } from '@nivo/sunburst'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// No data transformation needed - backend provides perfect format!

// Nivo Sunburst Component - Uses backend data directly
const TaskSetsSunburst: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  // Use the sunburst_data directly from backend - no processing needed!
  const sunburstData = useMemo(() => {
    if (!data?.sunburst_data || data.sunburst_data.length === 0) return null
    return data.sunburst_data[0] // Backend sends array with root node
  }, [data])

  if (!sunburstData) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <Database className="h-12 w-12 text-muted-foreground mx-auto" />
          <p className="text-muted-foreground">No data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full w-full bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-purple-50/50 dark:from-slate-900/20 dark:via-blue-900/15 dark:to-purple-900/20 rounded-xl p-2 border border-slate-200/50 dark:border-slate-700/30">
      <ResponsiveSunburst
        data={sunburstData}
        margin={{ top: 5, right: 5, bottom: 5, left: 5 }}
        id="name"
        value="value"
        cornerRadius={4}
        borderWidth={3}
        borderColor="rgba(255, 255, 255, 0.8)"
        colors={(node: any) => {
          // Enhanced color palette with better contrast
          const baseColor = node.data.itemStyle?.color || '#6B7280'

          // Add depth and vibrancy to colors
          const colorMap: { [key: string]: string } = {
            '#F54F4A': '#EF4444', // Bright red
            '#FF8C75': '#F97316', // Bright orange
            '#FFB499': '#F59E0B', // Bright amber
            '#4CAF50': '#10B981', // Bright emerald
            '#2196F3': '#3B82F6', // Bright blue
            '#FF9800': '#F59E0B', // Bright orange
            '#ddd': '#64748B'     // Slate for root
          }

          return colorMap[baseColor] || baseColor
        }}
        enableArcLabels={true}
        arcLabel={(node: any) => {
          // Show label only if arc is large enough
          const percentage = (node.value / (node.parent?.value || node.value)) * 100
          if (percentage < 6) return '' // Show more labels

          // Format labels nicely with better text
          const label = node.id.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())

          // Shorten long labels for better fit
          if (label.length > 12) {
            return label.split(' ').map((word: string) => word.charAt(0)).join('')
          }

          return label
        }}
        arcLabelsRadiusOffset={0.6}
        arcLabelsTextColor="#ffffff"
        arcLabelsSkipAngle={8}
        tooltip={({ id, value, color, data }: any) => (
          <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm p-4 rounded-xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center gap-3 mb-3">
              <div
                className="w-4 h-4 rounded-full shadow-sm"
                style={{ backgroundColor: color }}
              />
              <span className="font-bold text-lg">
                {id.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
              </span>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Task Sets:</span>
                <strong className="text-blue-600 dark:text-blue-400">{value}</strong>
              </div>
              {data.task_items_count && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Task Items:</span>
                  <strong className="text-emerald-600 dark:text-emerald-400">{data.task_items_count}</strong>
                </div>
              )}
              {data.task_items_count && (
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-500">Avg Items/Set:</span>
                    <span className="font-medium">{(data.task_items_count / value).toFixed(1)}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        animate={true}
        motionConfig="wobbly"
        transitionMode="pushIn"
        isInteractive={true}
      />
    </div>
  )
}

/**
 * Task Sets Analytics Card - Clean bar chart visualization
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="flex-1 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Target className="h-5 w-5" />
                Task Sets Distribution
              </h3>
              <p className="text-sm text-muted-foreground">Hierarchical sunburst view</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <p className="text-destructive">Failed to load chart data</p>
              <button
                onClick={onRefresh}
                className="text-sm text-primary hover:underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Target className="h-5 w-5" />
                Task Sets Distribution
              </h3>
              <p className="text-sm text-muted-foreground">Hierarchical sunburst view</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <Database className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No data available</p>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden h-full shadow-xl backdrop-blur-sm"
    >
      {/* Enhanced Background gradient with animation */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative space-y-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              Task Sets Distribution
            </h3>
            <p className="text-sm text-muted-foreground">Hierarchical sunburst view</p>
          </div>
          <button
            onClick={onRefresh}
            className="p-2 hover:bg-muted rounded-lg transition-colors group"
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
          </button>
        </div>

        {/* Chart Container - Larger size */}
        <div className="flex-1 min-h-0" style={{ minHeight: '400px' }}>
          <TaskSetsSunburst data={data} />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard