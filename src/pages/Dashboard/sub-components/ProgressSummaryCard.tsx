import React from 'react'
import { motion } from 'framer-motion'
import {
  TrendingUp,
  BarChart2,
  Calendar,
  Award,
  Clock,
  Target,
  CheckCircle
} from 'lucide-react'
import { cn } from '../../../utils/cn'

interface ProgressSummaryCardProps {
  cardVariants: any
  stats: {
    streakDays?: number
    totalLearningTime?: number
    completionRate?: number
    accuracyRate?: number
    totalPoints?: number
    level?: number
  } | null
}

const ProgressSummaryCard: React.FC<ProgressSummaryCardProps> = ({
  cardVariants,
  stats
}) => {
  // Default values if stats are null
  const defaultStats = {
    streakDays: 3,
    totalLearningTime: 120, // minutes
    completionRate: 75,
    accuracyRate: 82,
    totalPoints: 450,
    level: 4
  }

  const currentStats = stats || defaultStats

  // Format learning time
  const formatLearningTime = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden"
    >
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center gap-2">
          <BarChart2 className="h-5 w-5 text-purple-500" />
          Progress Summary
        </h3>
      </div>

      <div className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {/* Streak */}
          <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 border border-amber-200 dark:border-amber-800/50 rounded-xl p-3">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-amber-500 rounded-lg">
                <Calendar className="h-3.5 w-3.5 text-white" />
              </div>
              <h4 className="text-sm font-medium text-amber-800 dark:text-amber-300">
                Current Streak
              </h4>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold text-amber-900 dark:text-amber-100">
                {currentStats.streakDays}
              </span>
              <span className="ml-1 text-sm text-amber-700 dark:text-amber-400">days</span>
            </div>
          </div>

          {/* Learning Time */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-800/50 rounded-xl p-3">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-blue-500 rounded-lg">
                <Clock className="h-3.5 w-3.5 text-white" />
              </div>
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                Learning Time
              </h4>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {formatLearningTime(currentStats.totalLearningTime || 0)}
              </span>
            </div>
          </div>

          {/* Level */}
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border border-purple-200 dark:border-purple-800/50 rounded-xl p-3">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-purple-500 rounded-lg">
                <Award className="h-3.5 w-3.5 text-white" />
              </div>
              <h4 className="text-sm font-medium text-purple-800 dark:text-purple-300">
                Current Level
              </h4>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                {currentStats.level}
              </span>
              <span className="ml-1 text-sm text-purple-700 dark:text-purple-400">
                ({currentStats.totalPoints} pts)
              </span>
            </div>
          </div>
        </div>

        {/* Progress Bars */}
        <div className="mt-4 space-y-4">
          {/* Completion Rate */}
          <div>
            <div className="flex justify-between items-center mb-1.5">
              <div className="flex items-center gap-1.5">
                <CheckCircle className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                  Completion Rate
                </span>
              </div>
              <span className="text-sm font-bold text-green-600 dark:text-green-400">
                {currentStats.completionRate}%
              </span>
            </div>
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-700 ease-out"
                style={{ width: `${currentStats.completionRate}%` }}
              />
            </div>
          </div>

          {/* Accuracy Rate */}
          <div>
            <div className="flex justify-between items-center mb-1.5">
              <div className="flex items-center gap-1.5">
                <Target className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                  Accuracy Rate
                </span>
              </div>
              <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                {currentStats.accuracyRate}%
              </span>
            </div>
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-700 ease-out"
                style={{ width: `${currentStats.accuracyRate}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800/30 dark:to-slate-800/10 p-3 border-t border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1.5">
            <TrendingUp className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
            <span className="text-xs text-slate-700 dark:text-slate-300">
              Progress trending upward
            </span>
          </div>
          <button className="text-xs text-blue-600 dark:text-blue-400 hover:underline">
            View details
          </button>
        </div>
      </div>
    </motion.div>
  )
}

export default ProgressSummaryCard