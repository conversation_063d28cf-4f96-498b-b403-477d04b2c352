import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Calendar, Filter, RotateCcw } from 'lucide-react'
import { cn } from '../../../utils/cn'

interface DateFilterCardProps {
  startDate: string
  endDate: string
  onDateChange: (startDate: string, endDate: string) => void
  onReset: () => void
  cardVariants: any
}

/**
 * Date Filter Card - Provides date range filtering for dashboard metrics
 * Sleek design with preset options and custom date selection
 */
const DateFilterCard: React.FC<DateFilterCardProps> = ({
  startDate,
  endDate,
  onDateChange,
  onReset,
  cardVariants
}) => {
  const [isCustom, setIsCustom] = useState(false)

  // Preset date ranges
  const presets = [
    {
      label: 'Today',
      getValue: () => {
        const today = new Date().toISOString().split('T')[0]
        return { start: today, end: today }
      }
    },
    {
      label: 'Last 7 Days',
      getValue: () => {
        const end = new Date().toISOString().split('T')[0]
        const start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        return { start, end }
      }
    },
    {
      label: 'Last 30 Days',
      getValue: () => {
        const end = new Date().toISOString().split('T')[0]
        const start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        return { start, end }
      }
    },
    {
      label: 'This Month',
      getValue: () => {
        const now = new Date()
        const start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
        const end = new Date().toISOString().split('T')[0]
        return { start, end }
      }
    }
  ]

  const handlePresetClick = (preset: typeof presets[0]) => {
    const { start, end } = preset.getValue()
    onDateChange(start, end)
    setIsCustom(false)
  }

  const handleCustomDateChange = (field: 'start' | 'end', value: string) => {
    if (field === 'start') {
      onDateChange(value, endDate)
    } else {
      onDateChange(startDate, value)
    }
  }

  const isPresetActive = (preset: typeof presets[0]) => {
    const { start, end } = preset.getValue()
    return startDate === start && endDate === end
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-4"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-foreground">Date Filter</h3>
            <p className="text-xs text-muted-foreground">Select date range</p>
          </div>
        </div>
        <button
          onClick={onReset}
          className="p-2 hover:bg-muted rounded-md transition-colors"
          title="Reset to default"
        >
          <RotateCcw className="h-4 w-4 text-muted-foreground" />
        </button>
      </div>

      {/* Preset Buttons */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        {presets.map((preset) => (
          <motion.button
            key={preset.label}
            onClick={() => handlePresetClick(preset)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={cn(
              "px-3 py-2 text-xs font-medium rounded-lg transition-all duration-200",
              isPresetActive(preset)
                ? "bg-primary text-primary-foreground shadow-sm"
                : "bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground"
            )}
          >
            {preset.label}
          </motion.button>
        ))}
      </div>

      {/* Custom Date Toggle */}
      <button
        onClick={() => setIsCustom(!isCustom)}
        className={cn(
          "w-full px-3 py-2 text-xs font-medium rounded-lg transition-all duration-200 mb-3",
          isCustom
            ? "bg-secondary text-secondary-foreground"
            : "bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground"
        )}
      >
        <Calendar className="h-3 w-3 inline mr-2" />
        Custom Range
      </button>

      {/* Custom Date Inputs */}
      <motion.div
        initial={false}
        animate={{ height: isCustom ? 'auto' : 0, opacity: isCustom ? 1 : 0 }}
        transition={{ duration: 0.2 }}
        className="overflow-hidden"
      >
        <div className="space-y-3 pt-2">
          <div>
            <label className="text-xs font-medium text-muted-foreground block mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => handleCustomDateChange('start', e.target.value)}
              className="w-full px-3 py-2 text-xs bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
          <div>
            <label className="text-xs font-medium text-muted-foreground block mb-1">
              End Date
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => handleCustomDateChange('end', e.target.value)}
              className="w-full px-3 py-2 text-xs bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
        </div>
      </motion.div>

      {/* Current Range Display */}
      <div className="mt-4 pt-3 border-t border-border">
        <p className="text-xs text-muted-foreground">
          <span className="font-medium">Range:</span> {startDate} to {endDate}
        </p>
      </div>
    </motion.div>
  )
}

export default DateFilterCard
