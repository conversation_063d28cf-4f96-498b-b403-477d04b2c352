import React, { useState, useEffect, useCallback } from 'react'
import { useAppSelector } from '../../store/hooks'
import managementService, { 
  DashboardOverview, 
  UsersMetrics, 
  TaskSetsMetrics 
} from '../../services/management/managementService'

interface ManagementDashboardState {
  // Overview data
  overview: DashboardOverview | null
  overviewLoading: boolean
  overviewError: string | null

  // Users metrics
  usersMetrics: UsersMetrics | null
  usersLoading: boolean
  usersError: string | null

  // Task sets metrics
  taskSetsMetrics: TaskSetsMetrics | null
  taskSetsLoading: boolean
  taskSetsError: string | null

  // Date filtering
  startDate: string
  endDate: string
}

/**
 * Management Dashboard Container
 * Handles state management for the admin dashboard with management APIs
 */
const useManagementDashboard = () => {
  // Get default date range (last 7 days)
  const getDefaultDateRange = () => {
    const end = new Date().toISOString().split('T')[0]
    const start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    return { start, end }
  }

  const { start: defaultStart, end: defaultEnd } = getDefaultDateRange()

  const [state, setState] = useState<ManagementDashboardState>({
    overview: null,
    overviewLoading: false,
    overviewError: null,
    usersMetrics: null,
    usersLoading: false,
    usersError: null,
    taskSetsMetrics: null,
    taskSetsLoading: false,
    taskSetsError: null,
    startDate: defaultStart,
    endDate: defaultEnd
  })

  const user = useAppSelector(state => state.auth.user)

  // Load overview data (no date filtering)
  const loadOverview = useCallback(async () => {
    setState(prev => ({ ...prev, overviewLoading: true, overviewError: null }))
    
    try {
      const data = await managementService.getDashboardOverview()
      setState(prev => ({ ...prev, overview: data, overviewLoading: false }))
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        overviewLoading: false, 
        overviewError: error.message || 'Failed to load overview' 
      }))
    }
  }, [])

  // Load users metrics with date filtering
  const loadUsersMetrics = useCallback(async (startDate?: string, endDate?: string) => {
    setState(prev => ({ ...prev, usersLoading: true, usersError: null }))
    
    try {
      const data = await managementService.getUsersMetrics(startDate, endDate)
      setState(prev => ({ ...prev, usersMetrics: data, usersLoading: false }))
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        usersLoading: false, 
        usersError: error.message || 'Failed to load users metrics' 
      }))
    }
  }, [])

  // Load task sets metrics with date filtering
  const loadTaskSetsMetrics = useCallback(async (startDate?: string, endDate?: string) => {
    setState(prev => ({ ...prev, taskSetsLoading: true, taskSetsError: null }))
    
    try {
      const data = await managementService.getTaskSetsMetrics(startDate, endDate)
      setState(prev => ({ ...prev, taskSetsMetrics: data, taskSetsLoading: false }))
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        taskSetsLoading: false, 
        taskSetsError: error.message || 'Failed to load task sets metrics' 
      }))
    }
  }, [])

  // Handle date change
  const handleDateChange = useCallback((startDate: string, endDate: string) => {
    setState(prev => ({ ...prev, startDate, endDate }))
    // Reload metrics with new date range
    loadUsersMetrics(startDate, endDate)
    loadTaskSetsMetrics(startDate, endDate)
  }, [loadUsersMetrics, loadTaskSetsMetrics])

  // Reset to default date range
  const handleDateReset = useCallback(() => {
    const { start, end } = getDefaultDateRange()
    handleDateChange(start, end)
  }, [handleDateChange])

  // Refresh all data
  const refreshAll = useCallback(() => {
    loadOverview()
    loadUsersMetrics(state.startDate, state.endDate)
    loadTaskSetsMetrics(state.startDate, state.endDate)
  }, [loadOverview, loadUsersMetrics, loadTaskSetsMetrics, state.startDate, state.endDate])

  // Individual refresh functions
  const refreshOverview = useCallback(() => {
    loadOverview()
  }, [loadOverview])

  const refreshUsersMetrics = useCallback(() => {
    loadUsersMetrics(state.startDate, state.endDate)
  }, [loadUsersMetrics, state.startDate, state.endDate])

  const refreshTaskSetsMetrics = useCallback(() => {
    loadTaskSetsMetrics(state.startDate, state.endDate)
  }, [loadTaskSetsMetrics, state.startDate, state.endDate])

  // Initial load
  useEffect(() => {
    if (user) {
      loadOverview()
      loadUsersMetrics(state.startDate, state.endDate)
      loadTaskSetsMetrics(state.startDate, state.endDate)
    }
  }, [user, loadOverview, loadUsersMetrics, loadTaskSetsMetrics, state.startDate, state.endDate])

  return {
    // State
    ...state,
    user,
    
    // Actions
    handleDateChange,
    handleDateReset,
    refreshAll,
    refreshOverview,
    refreshUsersMetrics,
    refreshTaskSetsMetrics,
    
    // Computed
    isLoading: state.overviewLoading || state.usersLoading || state.taskSetsLoading,
    hasError: !!(state.overviewError || state.usersError || state.taskSetsError)
  }
}

export default useManagementDashboard
