import { User } from '../../types/auth'
import { LearningStats, LeaderboardResponse } from '../../services/stats/statsService'

export interface DashboardComponentProps {
  user: User
  stats: LearningStats | null
  statsLoading: boolean
  statsError: string | null
  onRefreshStats: () => void
  leaderboard: LeaderboardResponse | null
  leaderboardLoading: boolean
  leaderboardError: string | null
  onRefreshLeaderboard: () => void
}

export interface AnimationVariants {
  cardVariants: {
    hidden: { opacity: number; y: number }
    visible: { opacity: number; y: number }
    hover: {
      scale: number
      boxShadow: string
      transition: {
        type: string
        stiffness: number
        damping: number
      }
    }
  }
  containerVariants: {
    hidden: { opacity: number }
    visible: {
      opacity: number
      transition: {
        staggerChildren: number
      }
    }
  }
}
