import React from 'react'
import { useAppSelector } from '../../store/hooks'
import DashboardContainer from './Dashboard.container'
import ManagementDashboard from './ManagementDashboard.component'

/**
 * Dashboard page entry point
 * Shows management dashboard for admin users, regular dashboard for others
 */
const Dashboard: React.FC = () => {
  const user = useAppSelector(state => state.auth.user)

  // Show management dashboard for admin users
  if (user?.role === 'admin' || user?.role === 'agent') {
    return <ManagementDashboard />
  }

  // Show regular dashboard for regular users
  return <DashboardContainer />
}

export default Dashboard
