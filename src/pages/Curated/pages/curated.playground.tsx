import React, { useState, useCallback } from 'react'
import {
  Shield,
  AlertTriangle
} from 'lucide-react'
import SideNavigation from '../../../components/layout/SideNavigation'
import { PromptEditor, PromptHistory } from '../components'
import { useAppSelector } from '../../../store/hooks'

/**
 * Playground Page
 * Admin-only content generation interface
 * Provides prompt editor and generation history
 */
const Playground: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const user = useAppSelector(state => state.auth.user)

  // Check if user has admin privileges
  // Note: This should be replaced with proper role checking from your auth system
  const isAdmin = user?.role === 'admin' || user?.role === 'agent'

  const handleGenerate = useCallback((_prompt: string) => {
    // Trigger refresh of prompt history
    setRefreshTrigger(prev => prev + 1)
  }, [])

  // Admin access check
  if (!isAdmin) {
    return (
      <MainLayout
        title="⚡ Playground"
        description="Content generation workspace"
        className="h-screen flex items-center justify-center"
      >
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mb-4 mx-auto">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-bold mb-2">Access Restricted</h2>
          <p className="text-gray-600 mb-4">Admin privileges required</p>
          <div className="flex items-center gap-2 px-3 py-2 bg-yellow-100 rounded-lg">
            <AlertTriangle className="w-4 h-4 text-yellow-600" />
            <span className="text-sm text-yellow-800">Contact administrator</span>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar Navigation */}
      <SideNavigation />

      {/* Main Content */}
      <main className="flex-1 flex flex-col overflow-hidden min-w-0">
        {/* Header */}
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
          <div className="flex h-12 sm:h-14 xl:h-16 items-center px-3 sm:px-4 lg:px-6 xl:px-8">
            <div className="flex flex-col min-w-0 flex-1">
              <h1 className="text-sm sm:text-base lg:text-lg font-semibold text-foreground truncate">
                ⚡ Playground
              </h1>
              <p className="text-xs sm:text-sm text-muted-foreground truncate hidden sm:block">
                AI Content Generation
              </p>
            </div>
          </div>
        </header>

        {/* Fixed content area - fits exactly in viewport */}
        <div className="flex-1 overflow-hidden p-4 min-h-0">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 h-full max-h-[calc(100vh-8rem)]">
            {/* Prompt Editor - 60% width */}
            <div className="lg:col-span-3 min-h-0">
              <PromptEditor
                onGenerate={handleGenerate}
                className="h-full max-h-full"
              />
            </div>

            {/* Prompt History - 40% width */}
            <div className="lg:col-span-2 min-h-0">
              <PromptHistory
                refreshTrigger={refreshTrigger}
                className="h-full max-h-full"
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default Playground
