/**
 * Curated Content Module Index
 * Main entry point for curated content routing and exports
 */

// Page exports
export { default as Anthology } from './pages/curated.anthology'
export { default as Atelier } from './pages/curated.atelier'
export { default as Playground } from './pages/curated.playground'

// Component exports
export * from './components'

// Hook exports
export * from './hooks'

// Type exports
export type {
  Theme,
  ContentSet,
  GeneratedPrompt,
  Question,
  FilterOptions,
  PaginatedResponse,
  ApiResponse,
  ThemesQuery,
  ContentQuery
} from '../../services/curatedService'
