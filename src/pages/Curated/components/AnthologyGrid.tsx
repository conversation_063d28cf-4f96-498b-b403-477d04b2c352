import React from 'react'
import { motion } from 'framer-motion'
import { ContentCard, LoadingGrid, EmptyState } from '../../../components/curated'
import { ContentSet } from '../../../services/curatedService'
import { cn } from '../../../utils/cn'

export interface AnthologyGridProps {
  content: ContentSet[]
  loading?: boolean
  error?: string | null
  onContentClick?: (content: ContentSet) => void
  onRetry?: () => void
  className?: string
  showTheme?: boolean
}

/**
 * AnthologyGrid Component
 * Grid layout for displaying content sets in Anthology page
 * Handles loading, error, and empty states
 */
const AnthologyGrid: React.FC<AnthologyGridProps> = React.memo(({
  content,
  loading = false,
  error = null,
  onContentClick,
  onRetry,
  className,
  showTheme = true
}) => {
  if (loading) {
    return (
      <LoadingGrid 
        count={8} 
        type="content"
        className={cn(
          'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
          className
        )}
      />
    )
  }

  if (error) {
    return (
      <EmptyState
        type="error"
        title="Failed to load content"
        description={error}
        actionLabel="Try again"
        onAction={onRetry}
        className={className}
      />
    )
  }

  if (content.length === 0) {
    return (
      <EmptyState
        type="content"
        className={className}
      />
    )
  }

  return (
    <div className={cn(
      'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6',
      className
    )}>
      {content.map((item, index) => (
        <motion.div
          key={item._id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
        >
          <ContentCard
            content={item}
            onClick={onContentClick}
            showTheme={showTheme}
          />
        </motion.div>
      ))}
    </div>
  )
})

AnthologyGrid.displayName = 'AnthologyGrid'

export default AnthologyGrid
