import React from 'react'
import { motion } from 'framer-motion'
import { ThemeCard, LoadingGrid, EmptyState } from '../../../components/curated'
import { Theme } from '../../../services/curatedService'
import { cn } from '../../../utils/cn'

export interface AtelierGridProps {
  themes: Theme[]
  loading?: boolean
  error?: string | null
  onThemeClick?: (theme: Theme) => void
  onRetry?: () => void
  className?: string
  viewMode?: 'grid' | 'list'
  showStats?: boolean
}

/**
 * AtelierGrid Component
 * Grid/List layout for displaying themes in Atelier page
 * Handles loading, error, and empty states with view mode support
 */
const AtelierGrid: React.FC<AtelierGridProps> = React.memo(({
  themes,
  loading = false,
  error = null,
  onThemeClick,
  onRetry,
  className,
  viewMode = 'grid',
  showStats = true
}) => {
  if (loading) {
    return (
      <LoadingGrid 
        count={8} 
        type="theme"
        className={cn(
          viewMode === 'grid' 
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1',
          className
        )}
      />
    )
  }

  if (error) {
    return (
      <EmptyState
        type="error"
        title="Failed to load themes"
        description={error}
        actionLabel="Try again"
        onAction={onRetry}
        className={className}
      />
    )
  }

  if (themes.length === 0) {
    return (
      <EmptyState
        type="themes"
        className={className}
      />
    )
  }

  return (
    <div className={cn(
      'grid gap-6',
      viewMode === 'grid' 
        ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
        : 'grid-cols-1 max-w-4xl mx-auto',
      className
    )}>
      {themes.map((theme, index) => (
        <motion.div
          key={theme.id}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
        >
          <ThemeCard
            theme={theme}
            onClick={onThemeClick}
            showStats={showStats}
            className={cn(
              viewMode === 'list' && 'flex-row items-center p-4'
            )}
          />
        </motion.div>
      ))}
    </div>
  )
})

AtelierGrid.displayName = 'AtelierGrid'

export default AtelierGrid
