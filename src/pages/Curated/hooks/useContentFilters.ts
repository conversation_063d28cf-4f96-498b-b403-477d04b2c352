import { useState, useEffect, useCallback } from 'react'
import { 
  CuratedService, 
  FilterOptions, 
  ApiResponse 
} from '../../../services/curatedService'
import { FilterGroup } from '../../../components/curated/FilterPanel'

export interface UseContentFiltersReturn {
  filterOptions: FilterOptions | null
  filterGroups: FilterGroup[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

/**
 * Custom hook for managing content filter options
 * Fetches and formats filter options for use with FilterPanel component
 */
const useContentFilters = (): UseContentFiltersReturn => {
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchFilterOptions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response: ApiResponse<FilterOptions> = await CuratedService.getFilterOptions()
      setFilterOptions(response.data)
    } catch (err) {
      console.error('Error fetching filter options:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch filter options')
    } finally {
      setLoading(false)
    }
  }, [])

  const refetch = useCallback(async () => {
    await fetchFilterOptions()
  }, [fetchFilterOptions])

  // Convert filter options to FilterGroup format
  const filterGroups: FilterGroup[] = filterOptions ? [
    {
      key: 'theme_id',
      label: 'Theme',
      type: 'select',
      options: filterOptions.themes.map(theme => ({
        value: theme.id,
        label: theme.name_en
      }))
    },
    {
      key: 'status',
      label: 'Status',
      type: 'multiselect',
      options: filterOptions.status_options.map(status => ({
        value: status,
        label: status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
      }))
    },
    {
      key: 'gentype',
      label: 'Generation Type',
      type: 'multiselect',
      options: filterOptions.gentype_options.map(gentype => ({
        value: gentype,
        label: gentype.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
      }))
    },
    {
      key: 'difficulty_level',
      label: 'Difficulty Level',
      type: 'select',
      options: filterOptions.difficulty_levels.map(level => ({
        value: level.value.toString(),
        label: level.label
      }))
    }
  ] : []

  // Fetch filter options on mount
  useEffect(() => {
    fetchFilterOptions()
  }, [fetchFilterOptions])

  return {
    filterOptions,
    filterGroups,
    loading,
    error,
    refetch
  }
}

export default useContentFilters
