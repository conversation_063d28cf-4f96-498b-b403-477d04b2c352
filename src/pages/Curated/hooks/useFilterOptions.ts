import { useState, useEffect, useCallback, useRef } from 'react'
import { CuratedService, FilterOptions } from '../../../services/curatedService'

export interface UseFilterOptionsReturn {
  filterOptions: FilterOptions | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

/**
 * Custom hook for managing filter options with caching
 * Ensures filter options are only fetched once and cached
 */
const useFilterOptions = (): UseFilterOptionsReturn => {
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Use ref to prevent duplicate API calls
  const fetchedRef = useRef(false)
  const fetchingRef = useRef(false)

  const fetchFilterOptions = useCallback(async () => {
    // Prevent duplicate calls
    if (fetchedRef.current || fetchingRef.current) {
      return
    }

    try {
      fetchingRef.current = true
      setLoading(true)
      setError(null)

      console.log('🔍 Fetching filter options...')
      const response = await CuratedService.getFilterOptions()
      
      setFilterOptions(response.data)
      fetchedRef.current = true
      console.log('✅ Filter options cached successfully')
    } catch (err) {
      console.error('❌ Failed to fetch filter options:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch filter options')
      // Reset refs on error so we can retry
      fetchedRef.current = false
    } finally {
      setLoading(false)
      fetchingRef.current = false
    }
  }, [])

  const refetch = useCallback(async () => {
    // Reset cache and fetch again
    fetchedRef.current = false
    fetchingRef.current = false
    await fetchFilterOptions()
  }, [fetchFilterOptions])

  // Fetch filter options only once on mount
  useEffect(() => {
    if (!fetchedRef.current && !fetchingRef.current) {
      fetchFilterOptions()
    }
  }, [fetchFilterOptions])

  return {
    filterOptions,
    loading,
    error,
    refetch
  }
}

export default useFilterOptions
