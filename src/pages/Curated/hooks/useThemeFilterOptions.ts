import { useState, useEffect, useCallback, useRef } from 'react'
import { CuratedService, ThemeFilterOptions } from '../../../services/curatedService'

export interface UseThemeFilterOptionsReturn {
  filterOptions: ThemeFilterOptions | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

/**
 * Custom hook for managing theme filter options with caching
 * Ensures theme filter options are only fetched once and cached
 */
const useThemeFilterOptions = (): UseThemeFilterOptionsReturn => {
  const [filterOptions, setFilterOptions] = useState<ThemeFilterOptions | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Use ref to prevent duplicate API calls
  const fetchedRef = useRef(false)
  const fetchingRef = useRef(false)

  const fetchFilterOptions = useCallback(async () => {
    // Prevent duplicate calls
    if (fetchedRef.current || fetchingRef.current) {
      return
    }

    try {
      fetchingRef.current = true
      setLoading(true)
      setError(null)

      console.log('🔍 Fetching theme filter options...')
      const response = await CuratedService.getThemeFilterOptions()
      
      setFilterOptions(response.data)
      fetchedRef.current = true
      console.log('✅ Theme filter options cached successfully')
    } catch (err) {
      console.error('❌ Failed to fetch theme filter options:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch theme filter options')
      // Reset refs on error so we can retry
      fetchedRef.current = false
    } finally {
      setLoading(false)
      fetchingRef.current = false
    }
  }, [])

  const refetch = useCallback(async () => {
    // Reset cache and fetch again
    fetchedRef.current = false
    fetchingRef.current = false
    await fetchFilterOptions()
  }, [fetchFilterOptions])

  // Fetch filter options only once on mount
  useEffect(() => {
    if (!fetchedRef.current && !fetchingRef.current) {
      fetchFilterOptions()
    }
  }, [fetchFilterOptions])

  return {
    filterOptions,
    loading,
    error,
    refetch
  }
}

export default useThemeFilterOptions
