export interface OnboardingFormData {
  age: number
  difficulty_level: number
  preferred_topics: string[]
}

export interface OnboardingFormErrors {
  age?: string
  difficulty_level?: string
  preferred_topics?: string
  general?: string
}

export interface OnboardingContainerProps {
  // Props passed from route/parent
}

export interface OnboardingComponentProps {
  formData: OnboardingFormData
  errors: OnboardingFormErrors
  isLoading: boolean
  onInputChange: (field: keyof OnboardingFormData, value: any) => void
  onSubmit: (e: React.FormEvent) => void
  onClearError: () => void
}

export interface DifficultyOption {
  value: number
  label: string
  description: string
}

export interface TopicOption {
  value: string
  label: string
  icon?: string
}

export interface CharacterOption {
  id: string
  name: string
  gender: 'male' | 'female'
  emoji: string
  description: string
  live2dModel?: string
  color: string
}

export interface GenderOption {
  value: 'male' | 'female'
  label: string
  icon: string
  description: string
}

export interface CharacterOption {
  id: string
  name: string
  type: string
  gender: 'male' | 'female'
  preview: string
  description: string
  live2dModel?: string
}
